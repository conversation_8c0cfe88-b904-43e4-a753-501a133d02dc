import {
  Controller,
  ControllerBase,
  HttpGet,
  <PERSON>ttpPost,
  HttpPut,
  HttpDelete,
  Param,
  Body,
  Query,
  Inject,
  Authorized,
} from '@c-cam/core';
import TimeOffScheduleService from '@/services/TimeOffScheduleService';
import { TimeOffScheduleDocument } from '@/database/entities/TimeOffScheduleModel';

/**
 * Controller for managing time off schedules
 */
@Controller('/api/time-off-schedules')
@Authorized()
export class TimeOffScheduleController extends ControllerBase {
  constructor(
    @Inject(TimeOffScheduleService)
    private readonly timeOffScheduleService: TimeOffScheduleService,
  ) {
    super();
  }

  /**
   * Get all time off schedules
   */
  @HttpGet('/')
  async getAllTimeOffSchedules(
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('tenant_id') tenantId?: string,
    @Query('status') status?: string,
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : undefined,
        skip: skip ? parseInt(skip.toString()) : undefined,
        sort: sortBy ? { [sortBy]: sortDirection === 'desc' ? -1 : 1 } : undefined,
      };

      let timeOffSchedules: TimeOffScheduleDocument[];
      
      if (tenantId) {
        timeOffSchedules = await this.timeOffScheduleService.findByTenantId(tenantId);
      } else if (status === 'active') {
        timeOffSchedules = await this.timeOffScheduleService.findActive();
      } else {
        timeOffSchedules = await this.timeOffScheduleService.findAll();
      }

      return {
        time_off_schedules: timeOffSchedules,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve time off schedules: ${error}`);
    }
  }

  /**
   * Get time off schedule by ID
   */
  @HttpGet('/:id')
  async getTimeOffScheduleById(@Param('id') id: string) {
    try {
      const timeOffSchedule = await this.timeOffScheduleService.findById(id);
      if (!timeOffSchedule) {
        throw new Error('Time off schedule not found');
      }
      return { time_off_schedule: timeOffSchedule };
    } catch (error) {
      throw new Error(`Failed to retrieve time off schedule: ${error}`);
    }
  }

  /**
   * Create a new time off schedule
   */
  @HttpPost('/')
  async createTimeOffSchedule(@Body() timeOffScheduleData: Partial<TimeOffScheduleDocument>) {
    try {
      const newTimeOffSchedule = await this.timeOffScheduleService.create(timeOffScheduleData);
      return { time_off_schedule: newTimeOffSchedule };
    } catch (error) {
      throw new Error(`Failed to create time off schedule: ${error}`);
    }
  }

  /**
   * Update a time off schedule
   */
  @HttpPut('/:id')
  async updateTimeOffSchedule(
    @Param('id') id: string,
    @Body() timeOffScheduleData: Partial<TimeOffScheduleDocument>,
  ) {
    try {
      const updated = await this.timeOffScheduleService.update(id, timeOffScheduleData);
      if (!updated) {
        throw new Error('Time off schedule not found');
      }
      return { message: 'Time off schedule updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update time off schedule: ${error}`);
    }
  }

  /**
   * Delete a time off schedule
   */
  @HttpDelete('/:id')
  async deleteTimeOffSchedule(@Param('id') id: string) {
    try {
      const deleted = await this.timeOffScheduleService.delete(id);
      if (!deleted) {
        throw new Error('Time off schedule not found');
      }
      return { message: 'Time off schedule deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete time off schedule: ${error}`);
    }
  }

  /**
   * Get time off schedules by date range
   */
  @HttpGet('/date-range')
  async getTimeOffSchedulesByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    try {
      if (!startDate || !endDate) {
        throw new Error('startDate and endDate are required');
      }

      const start = new Date(startDate);
      const end = new Date(endDate);
      
      const timeOffSchedules = await this.timeOffScheduleService.findByDateRange(start, end);
      return { time_off_schedules: timeOffSchedules };
    } catch (error) {
      throw new Error(`Failed to retrieve time off schedules: ${error}`);
    }
  }
}
