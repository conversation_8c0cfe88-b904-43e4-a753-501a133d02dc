import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Inject,

  Authorized,
  Param,
  Query,
  UnauthorizedError,
} from '@c-cam/core';
import ShiftService from '../services/ShiftService';
import { getClassPolicy, createAuthorizedOptions, methodRef } from '../utils/controller-policies.js';

@Controller('/api/shifts')
@Authorized({ policies: [getClassPolicy(ShiftController.name)] })
export class ShiftController extends ControllerBase {
  constructor(
    @Inject(ShiftService) private shiftService: ShiftService,
  ) {
    super();
  }

  /**
   * Get all shifts
   */
  @HttpGet('/')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('getShifts')))
  async getShifts(
    @HttpContext() c: Context,
    @Query('limit') limit?: string,
    @Query('skip') skip?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);

    const shifts = await this.shiftService.find({
      limit: limit ? parseInt(limit) : undefined,
      skip: skip ? parseInt(skip) : undefined,
      sortBy,
      sortDirection,
    });

    return this.success(c, { shifts });
  }

  /**
   * Get a shift by ID
   */
  @HttpGet('/:id')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('getShiftById')))
  async getShiftById(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!id, 'Shift ID is required');

    const shift = await this.shiftService.findById(id);
    this.notFoundIf(!shift, 'Shift not found');

    return this.success(c, { shift });
  }

  /**
   * Create a new shift
   */
  @HttpPost('/')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('createShift')))
  async createShift(@HttpContext() c: Context): Promise<Response> {
    this.getCurrentUser(c.req);
    const shiftData = await c.req.json();

    // Validate required fields
    this.validateRequiredFields(shiftData, ['name', 'shift_type', 'work_coefficient']);

    // Sanitize the data
    const sanitizedData = this.sanitizeData(shiftData);

    const shift = await this.shiftService.createShift(sanitizedData);
    return this.created(c, { shift }, 'Shift created successfully');
  }

  /**
   * Create a new shift with shift detail
   */
  @HttpPost('/with-detail')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('createShiftWithDetail')))
  async createShiftWithDetail(@HttpContext() c: Context): Promise<Response> {
    const user = await this.getCurrentUser(c.req);
    const requestData = await c.req.json();
    const { shiftData, shiftDetailData } = requestData;

    // Validate required fields for shift
    this.validateRequiredFields(shiftData, ['name', 'type', 'work_coefficient', 'tenant_id']);

    // Create shift first
    const newShift = await this.shiftService.createShiftWithAutoCode({
      ...shiftData,
      created_by: user?.id || user?.sub,
    });

    // Import ShiftDetailService and Repository
    const ShiftDetailService = (await import('@/services/ShiftDetailService')).default;
    const ShiftDetailRepository = (await import('@/repositories/ShiftDetailRepository')).default;
    const shiftDetailRepository = new ShiftDetailRepository();
    const shiftDetailService = new ShiftDetailService(shiftDetailRepository);

    // Create shift detail with the shift ID
    const newShiftDetail = await shiftDetailService.createShiftDetailWithAutoCode({
      ...shiftDetailData,
      tenant_id: shiftData.tenant_id,
      created_by: user?.id || user?.sub,
    });

    // Update shift with shift_detail_id
    await this.shiftService.update((newShift as any)._id.toString(), {
      shift_detail_id: (newShiftDetail as any)._id.toString(),
    });

    return this.created(c, {
      shift: newShift,
      shiftDetail: newShiftDetail
    }, 'Shift with detail created successfully');
  }

  /**
   * Update a shift
   */
  @HttpPut('/:id')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('updateShift')))
  async updateShift(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!id, 'Shift ID is required');

    const shiftData = await c.req.json();

    // Sanitize the data
    const sanitizedData = this.sanitizeData(shiftData);

    const success = await this.shiftService.updateShift(id, sanitizedData);
    this.validateIf(!success, 'Failed to update shift');

    return this.success(c, { success: true }, 'Shift updated successfully');
  }

  /**
   * Delete a shift
   */
  @HttpDelete('/:id')
  @Authorized(createAuthorizedOptions(ShiftController.name, methodRef<ShiftController>('deleteShift')))
  async deleteShift(
    @HttpContext() c: Context,
    @Param('id') id: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!id, 'Shift ID is required');

    const success = await this.shiftService.delete(id);
    this.validateIf(!success, 'Failed to delete shift');

    return this.success(c, { success: true }, 'Shift deleted successfully');
  }

  /**
   * Find a shift by name
   */
  @HttpGet('/name/:name')
  async getShiftByName(
    @HttpContext() c: Context,
    @Param('name') name: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!name, 'Shift name is required');

    const shift = await this.shiftService.findByName(name);
    this.notFoundIf(!shift, 'Shift not found');

    return this.success(c, { shift });
  }

  /**
   * Find shifts by shift type
   */
  @HttpGet('/type/:shiftType')
  async getShiftsByShiftType(
    @HttpContext() c: Context,
    @Param('shiftType') shiftType: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!shiftType, 'Shift type is required');

    const shifts = await this.shiftService.findByShiftType(shiftType);
    return this.success(c, { shifts });
  }

  /**
   * Find shifts by work coefficient
   */
  @HttpGet('/coefficient/:workCoefficient')
  async getShiftsByWorkCoefficient(
    @HttpContext() c: Context,
    @Param('workCoefficient') workCoefficient: string,
  ): Promise<Response> {
    this.getCurrentUser(c.req);
    this.validateIf(!workCoefficient, 'Work coefficient is required');

    const coefficient = parseFloat(workCoefficient);
    this.validateIf(isNaN(coefficient), 'Invalid work coefficient');

    const shifts = await this.shiftService.findByWorkCoefficient(coefficient);
    return this.success(c, { shifts });
  }
}
