import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  Inject,
  UseGuards,
} from '@c-cam/core';
import { AuthGuard } from '@/guards/auth.guard';
import ShiftAssignmentDetailService from '@/services/ShiftAssignmentDetailService';
import { ShiftAssignmentDetailDocument } from '@/database/entities/ShiftAssignmentDetailModel';

/**
 * Controller for managing shift assignment details
 */
@Controller('/shift-assignment-details')
@UseGuards(AuthGuard)
export class ShiftAssignmentDetailController {
  constructor(
    @Inject(ShiftAssignmentDetailService)
    private readonly shiftAssignmentDetailService: ShiftAssignmentDetailService,
  ) {}

  /**
   * Get all shift assignment details
   */
  @Get('/')
  async getAllShiftAssignmentDetails(
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('tenant_id') tenantId?: string,
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : undefined,
        skip: skip ? parseInt(skip.toString()) : undefined,
        sort: sortBy ? { [sortBy]: sortDirection === 'desc' ? -1 : 1 } : undefined,
      };

      let shiftAssignmentDetails: ShiftAssignmentDetailDocument[];
      
      if (tenantId) {
        shiftAssignmentDetails = await this.shiftAssignmentDetailService.findByTenantId(tenantId);
      } else {
        shiftAssignmentDetails = await this.shiftAssignmentDetailService.findAll(options);
      }

      return {
        shift_assignment_details: shiftAssignmentDetails,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignment details: ${error}`);
    }
  }

  /**
   * Get shift assignment detail by ID
   */
  @Get('/:id')
  async getShiftAssignmentDetailById(@Param('id') id: string) {
    try {
      const shiftAssignmentDetail = await this.shiftAssignmentDetailService.findById(id);
      if (!shiftAssignmentDetail) {
        throw new Error('Shift assignment detail not found');
      }
      return { shift_assignment_detail: shiftAssignmentDetail };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignment detail: ${error}`);
    }
  }

  /**
   * Create a new shift assignment detail
   */
  @Post('/')
  async createShiftAssignmentDetail(@Body() shiftAssignmentDetailData: Partial<ShiftAssignmentDetailDocument>) {
    try {
      const newShiftAssignmentDetail = await this.shiftAssignmentDetailService.createWithAutoCode(shiftAssignmentDetailData);
      return { shift_assignment_detail: newShiftAssignmentDetail };
    } catch (error) {
      throw new Error(`Failed to create shift assignment detail: ${error}`);
    }
  }

  /**
   * Update a shift assignment detail
   */
  @Put('/:id')
  async updateShiftAssignmentDetail(
    @Param('id') id: string,
    @Body() shiftAssignmentDetailData: Partial<ShiftAssignmentDetailDocument>,
  ) {
    try {
      const updated = await this.shiftAssignmentDetailService.update(id, shiftAssignmentDetailData);
      if (!updated) {
        throw new Error('Shift assignment detail not found');
      }
      return { message: 'Shift assignment detail updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update shift assignment detail: ${error}`);
    }
  }

  /**
   * Delete a shift assignment detail
   */
  @Delete('/:id')
  async deleteShiftAssignmentDetail(@Param('id') id: string) {
    try {
      const deleted = await this.shiftAssignmentDetailService.delete(id);
      if (!deleted) {
        throw new Error('Shift assignment detail not found');
      }
      return { message: 'Shift assignment detail deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete shift assignment detail: ${error}`);
    }
  }
}
