import {
  Controller,
  ControllerBase,
  HttpGet,
  HttpPost,
  HttpPut,
  HttpDelete,
  Param,
  Body,
  Query,
  Inject,
  Authorized,
} from '@c-cam/core';
import ShiftAssignmentDetailService from '@/services/ShiftAssignmentDetailService';
import { ShiftAssignmentDetailDocument } from '@/database/entities/ShiftAssignmentDetailModel';

/**
 * Controller for managing shift assignment details
 */
@Controller('/api/shift-assignment-details')
@Authorized()
export class ShiftAssignmentDetailController extends ControllerBase {
  constructor(
    @Inject(ShiftAssignmentDetailService)
    private readonly shiftAssignmentDetailService: ShiftAssignmentDetailService,
  ) {
    super();
  }

  /**
   * Get all shift assignment details
   */
  @HttpGet('/')
  async getAllShiftAssignmentDetails(
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('tenant_id') tenantId?: string,
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : undefined,
        skip: skip ? parseInt(skip.toString()) : undefined,
        sort: sortBy ? { [sortBy]: sortDirection === 'desc' ? -1 : 1 } : undefined,
      };

      let shiftAssignmentDetails: ShiftAssignmentDetailDocument[];
      
      if (tenantId) {
        shiftAssignmentDetails = await this.shiftAssignmentDetailService.findByTenantId(tenantId);
      } else {
        shiftAssignmentDetails = await this.shiftAssignmentDetailService.findAll();
      }

      return {
        shift_assignment_details: shiftAssignmentDetails,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignment details: ${error}`);
    }
  }

  /**
   * Get shift assignment detail by ID
   */
  @HttpGet('/:id')
  async getShiftAssignmentDetailById(@Param('id') id: string) {
    try {
      const shiftAssignmentDetail = await this.shiftAssignmentDetailService.findById(id);
      if (!shiftAssignmentDetail) {
        throw new Error('Shift assignment detail not found');
      }
      return { shift_assignment_detail: shiftAssignmentDetail };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignment detail: ${error}`);
    }
  }

  /**
   * Create a new shift assignment detail
   */
  @HttpPost('/')
  async createShiftAssignmentDetail(@Body() shiftAssignmentDetailData: Partial<ShiftAssignmentDetailDocument>) {
    try {
      const newShiftAssignmentDetail = await this.shiftAssignmentDetailService.createWithAutoCode(shiftAssignmentDetailData);
      return { shift_assignment_detail: newShiftAssignmentDetail };
    } catch (error) {
      throw new Error(`Failed to create shift assignment detail: ${error}`);
    }
  }

  /**
   * Update a shift assignment detail
   */
  @HttpPut('/:id')
  async updateShiftAssignmentDetail(
    @Param('id') id: string,
    @Body() shiftAssignmentDetailData: Partial<ShiftAssignmentDetailDocument>,
  ) {
    try {
      const updated = await this.shiftAssignmentDetailService.update(id, shiftAssignmentDetailData);
      if (!updated) {
        throw new Error('Shift assignment detail not found');
      }
      return { message: 'Shift assignment detail updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update shift assignment detail: ${error}`);
    }
  }

  /**
   * Delete a shift assignment detail
   */
  @HttpDelete('/:id')
  async deleteShiftAssignmentDetail(@Param('id') id: string) {
    try {
      const deleted = await this.shiftAssignmentDetailService.delete(id);
      if (!deleted) {
        throw new Error('Shift assignment detail not found');
      }
      return { message: 'Shift assignment detail deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete shift assignment detail: ${error}`);
    }
  }
}
