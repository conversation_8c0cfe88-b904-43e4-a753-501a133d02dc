import {
  Controller,
  ControllerBase,
  HttpGet,
  <PERSON>ttpPost,
  HttpPut,
  HttpDelete,
  Param,
  Body,
  Query,
  Inject,
  Authorized,
} from '@c-cam/core';
import ShiftAssignmentService from '@/services/ShiftAssignmentService';
import { ShiftAssignmentDocument } from '@/database/entities/ShiftAssignmentModel';

/**
 * Controller for managing shift assignments
 */
@Controller('/api/shift-assignments')
@Authorized()
export class ShiftAssignmentController extends ControllerBase {
  constructor(
    @Inject(ShiftAssignmentService)
    private readonly shiftAssignmentService: ShiftAssignmentService,
  ) {
    super();
  }

  /**
   * Get all shift assignments
   */
  @HttpGet('/')
  async getAllShiftAssignments(
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('tenant_id') tenantId?: string,
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : undefined,
        skip: skip ? parseInt(skip.toString()) : undefined,
        sort: sortBy ? { [sortBy]: sortDirection === 'desc' ? -1 : 1 } : undefined,
      };

      let shiftAssignments: ShiftAssignmentDocument[];
      
      if (tenantId) {
        shiftAssignments = await this.shiftAssignmentService.findByTenantId(tenantId);
      } else {
        shiftAssignments = await this.shiftAssignmentService.findAll();
      }

      return {
        shift_assignments: shiftAssignments,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignments: ${error}`);
    }
  }

  /**
   * Get shift assignment by ID
   */
  @HttpGet('/:id')
  async getShiftAssignmentById(@Param('id') id: string) {
    try {
      const shiftAssignment = await this.shiftAssignmentService.findById(id);
      if (!shiftAssignment) {
        throw new Error('Shift assignment not found');
      }
      return { shift_assignment: shiftAssignment };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignment: ${error}`);
    }
  }

  /**
   * Create a new shift assignment
   */
  @HttpPost('/')
  async createShiftAssignment(@Body() shiftAssignmentData: Partial<ShiftAssignmentDocument>) {
    try {
      const newShiftAssignment = await this.shiftAssignmentService.create(shiftAssignmentData);
      return { shift_assignment: newShiftAssignment };
    } catch (error) {
      throw new Error(`Failed to create shift assignment: ${error}`);
    }
  }

  /**
   * Update a shift assignment
   */
  @HttpPut('/:id')
  async updateShiftAssignment(
    @Param('id') id: string,
    @Body() shiftAssignmentData: Partial<ShiftAssignmentDocument>,
  ) {
    try {
      const updated = await this.shiftAssignmentService.update(id, shiftAssignmentData);
      if (!updated) {
        throw new Error('Shift assignment not found');
      }
      return { message: 'Shift assignment updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update shift assignment: ${error}`);
    }
  }

  /**
   * Delete a shift assignment
   */
  @HttpDelete('/:id')
  async deleteShiftAssignment(@Param('id') id: string) {
    try {
      const deleted = await this.shiftAssignmentService.delete(id);
      if (!deleted) {
        throw new Error('Shift assignment not found');
      }
      return { message: 'Shift assignment deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete shift assignment: ${error}`);
    }
  }

  /**
   * Get shift assignments by shift ID
   */
  @HttpGet('/shift/:shiftId')
  async getShiftAssignmentsByShiftId(@Param('shiftId') shiftId: string) {
    try {
      const shiftAssignments = await this.shiftAssignmentService.findByShiftId(shiftId);
      return { shift_assignments: shiftAssignments };
    } catch (error) {
      throw new Error(`Failed to retrieve shift assignments: ${error}`);
    }
  }
}
