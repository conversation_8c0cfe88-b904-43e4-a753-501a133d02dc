import { Context } from 'hono';
import {
  Controller,
  ControllerBase,
  HttpContext,
  HttpGet,
  HttpPost,
  HttpPut,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  Inject,

  Authorized,
} from '@c-cam/core';
import ShiftDetailService from '../services/ShiftDetailService';
import { getClassPolicy, createAuthorizedOptions, methodRef } from '../utils/controller-policies.js';

@Controller('/api/shift-details')
@Authorized({ policies: [getClassPolicy(ShiftDetailController.name)] })
export class ShiftDetailController extends ControllerBase {
  constructor(
    @Inject(ShiftDetailService) private shiftDetailService: ShiftDetailService,
  ) {
    super();
  }

  /**
   * Get all shift details
   */
  @HttpGet('/')
  @Authorized(createAuthorizedOptions(ShiftDetailController.name, methodRef<ShiftDetailController>('getShiftDetails')))
  async getShiftDetails(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { limit, skip, sortBy, sortDirection } = c.req.query();

      const shiftDetails = await this.shiftDetailService.find({
        limit: limit ? parseInt(limit) : undefined,
        skip: skip ? parseInt(skip) : undefined,
        sortBy,
        sortDirection,
      });

      return c.json({ shiftDetails });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Get a shift detail by ID
   */
  @HttpGet('/:id')
  @Authorized(createAuthorizedOptions(ShiftDetailController.name, methodRef<ShiftDetailController>('getShiftDetailById')))
  async getShiftDetailById(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Shift detail ID is required' }, 400);
      }
      const shiftDetail = await this.shiftDetailService.findById(id);

      if (!shiftDetail) {
        return c.json({ error: 'Shift detail not found' }, 404);
      }

      return c.json({ shiftDetail });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Create a new shift detail
   */
  @HttpPost('/')
  @Authorized(createAuthorizedOptions(ShiftDetailController.name, methodRef<ShiftDetailController>('createShiftDetail')))
  async createShiftDetail(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const shiftDetailData = await c.req.json();

      // Validate required fields
      const requiredFields = ['shift_id', 'check_in_start_time', 'check_out_start_time', 'total_working_hours'];
      for (const field of requiredFields) {
        if (!shiftDetailData[field]) {
          return c.json({ error: `${field} is required` }, 400);
        }
      }

      const shiftDetail = await this.shiftDetailService.createShiftDetail(shiftDetailData);
      return c.json({ shiftDetail }, 201);
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Update a shift detail
   */
  @HttpPut('/:id')
  @Authorized(createAuthorizedOptions(ShiftDetailController.name, methodRef<ShiftDetailController>('updateShiftDetail')))
  async updateShiftDetail(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Shift detail ID is required' }, 400);
      }
      const shiftDetailData = await c.req.json();

      const success = await this.shiftDetailService.updateShiftDetail(id, shiftDetailData);

      if (!success) {
        return c.json({ error: 'Failed to update shift detail' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }

  /**
   * Delete a shift detail
   */
  @HttpDelete('/:id')
  @Authorized(createAuthorizedOptions(ShiftDetailController.name, methodRef<ShiftDetailController>('deleteShiftDetail')))
  async deleteShiftDetail(@HttpContext() c: Context): Promise<Response> {
    try {
      const userId = c.get('userId');
      if (!userId) {
        return c.json({ error: 'Unauthorized' }, 401);
      }

      const { id } = c.req.param();
      if (!id) {
        return c.json({ error: 'Shift detail ID is required' }, 400);
      }
      const success = await this.shiftDetailService.delete(id);

      if (!success) {
        return c.json({ error: 'Failed to delete shift detail' }, 400);
      }

      return c.json({ success: true });
    } catch (error: any) {
      return c.json({ error: error.message }, 500);
    }
  }








}
