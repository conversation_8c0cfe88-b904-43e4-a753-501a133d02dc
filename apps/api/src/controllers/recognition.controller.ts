import {
  <PERSON>,
  ControllerBase,
  HttpGet,
  <PERSON>ttp<PERSON>ost,
  HttpPut,
  <PERSON>ttpDelete,
  Param,
  Body,
  Query,
  Inject,
  Authorized,
} from '@c-cam/core';
import RecognitionService from '@/services/RecognitionService';
import { RecognitionDocument } from '@/database/entities/RecognitionModel';

/**
 * Controller for managing recognition logs
 */
@Controller('/api/recognitions')
@Authorized()
export class RecognitionController extends ControllerBase {
  constructor(
    @Inject(RecognitionService)
    private readonly recognitionService: RecognitionService,
  ) {
    super();
  }

  /**
   * Get all recognition logs
   */
  @HttpGet('/')
  async getAllRecognitions(
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('tenant_id') tenantId?: string,
    @Query('recognize_id') recognizeId?: string,
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : undefined,
        skip: skip ? parseInt(skip.toString()) : undefined,
        sort: sortBy ? { [sortBy]: sortDirection === 'desc' ? -1 : 1 } : { updatedAt: -1 },
      };

      let recognitions: RecognitionDocument[];
      
      if (tenantId) {
        recognitions = await this.recognitionService.findByTenantId(tenantId);
      } else if (recognizeId) {
        recognitions = await this.recognitionService.findByRecognizeIds([recognizeId]);
      } else {
        recognitions = await this.recognitionService.findAll();
      }

      return {
        recognitions: recognitions,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve recognitions: ${error}`);
    }
  }

  /**
   * Get recognition by ID
   */
  @HttpGet('/:id')
  async getRecognitionById(@Param('id') id: string) {
    try {
      const recognition = await this.recognitionService.findById(id);
      if (!recognition) {
        throw new Error('Recognition not found');
      }
      return { recognition: recognition };
    } catch (error) {
      throw new Error(`Failed to retrieve recognition: ${error}`);
    }
  }

  /**
   * Create a new recognition log
   */
  @HttpPost('/')
  async createRecognition(@Body() recognitionData: Partial<RecognitionDocument>) {
    try {
      const newRecognition = await this.recognitionService.create(recognitionData);
      return { recognition: newRecognition };
    } catch (error) {
      throw new Error(`Failed to create recognition: ${error}`);
    }
  }

  /**
   * Update a recognition log
   */
  @HttpPut('/:id')
  async updateRecognition(
    @Param('id') id: string,
    @Body() recognitionData: Partial<RecognitionDocument>,
  ) {
    try {
      const updated = await this.recognitionService.update(id, recognitionData);
      if (!updated) {
        throw new Error('Recognition not found');
      }
      return { message: 'Recognition updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update recognition: ${error}`);
    }
  }

  /**
   * Delete a recognition log
   */
  @HttpDelete('/:id')
  async deleteRecognition(@Param('id') id: string) {
    try {
      const deleted = await this.recognitionService.delete(id);
      if (!deleted) {
        throw new Error('Recognition not found');
      }
      return { message: 'Recognition deleted successfully' };
    } catch (error) {
      throw new Error(`Failed to delete recognition: ${error}`);
    }
  }

  /**
   * Get recognitions by date range
   */
  @HttpGet('/date-range')
  async getRecognitionsByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('recognize_ids') recognizeIds?: string,
  ) {
    try {
      if (!startDate || !endDate) {
        throw new Error('startDate and endDate are required');
      }

      const start = new Date(startDate);
      const end = new Date(endDate);
      
      let recognitions: RecognitionDocument[];
      
      if (recognizeIds) {
        const idsArray = recognizeIds.split(',');
        recognitions = await this.recognitionService.findByUserMappingAndDateRange(idsArray, start, end);
      } else {
        recognitions = await this.recognitionService.findByDateRange(start, end);
      }
      
      return { recognitions: recognitions };
    } catch (error) {
      throw new Error(`Failed to retrieve recognitions: ${error}`);
    }
  }

  /**
   * Get recognitions by multiple recognize IDs
   */
  @HttpPost('/by-recognize-ids')
  async getRecognitionsByRecognizeIds(@Body() body: { recognize_ids: string[] }) {
    try {
      if (!body.recognize_ids || !Array.isArray(body.recognize_ids)) {
        throw new Error('recognize_ids array is required');
      }

      const recognitions = await this.recognitionService.findByRecognizeIds(body.recognize_ids);
      return { recognitions: recognitions };
    } catch (error) {
      throw new Error(`Failed to retrieve recognitions: ${error}`);
    }
  }
}
