import ShiftModel, { ShiftDocument } from '@/database/entities/ShiftModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing shifts
 * Extends the BaseRepository with ShiftDocument type
 */
@Injectable()
class ShiftRepository extends Repository<ShiftDocument> {
  constructor() {
    super(ShiftModel);
  }

  /**
   * Find a shift by name
   * @param name The shift name to search for
   * @returns A promise that resolves to a shift or null if not found
   */
  async findByName(name: string): Promise<ShiftDocument | null> {
    return this.findOne({ name });
  }

  /**
   * Find shifts by shift type
   * @param shiftType The shift type to search for
   * @returns A promise that resolves to an array of shifts
   */
  async findByShiftType(shiftType: string): Promise<ShiftDocument[]> {
    return this.find({ shift_type: shiftType });
  }

  /**
   * Find shifts by work coefficient
   * @param workCoefficient The work coefficient to search for
   * @returns A promise that resolves to an array of shifts
   */
  async findByWorkCoefficient(
    workCoefficient: number,
  ): Promise<ShiftDocument[]> {
    return this.find({ work_coefficient: workCoefficient });
  }

  /**
   * Find shifts by tenant ID
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of shifts
   */
  async findByTenantId(tenantId: string): Promise<ShiftDocument[]> {
    return this.find({ tenant_id: tenantId });
  }

  /**
   * Get next code for tenant
   * @param tenantId The tenant ID
   * @returns A promise that resolves to the next code number
   */
  async getNextCode(tenantId: string): Promise<number> {
    const lastRecord = await this.getModel()
      .findOne({ tenant_id: tenantId })
      .sort({ code: -1 })
      .exec();

    return lastRecord ? lastRecord.code + 1 : 1;
  }
}

export default ShiftRepository;
