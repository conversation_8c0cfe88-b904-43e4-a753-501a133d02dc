import { BaseRepository, Injectable } from '@c-cam/core';
import { ShiftAssignmentDetailDocument } from '@/database/entities/ShiftAssignmentDetailModel';
import ShiftAssignmentDetailModel from '@/database/entities/ShiftAssignmentDetailModel';

/**
 * Repository for managing shift assignment details
 * Extends the BaseRepository with ShiftAssignmentDetailDocument type
 */
@Injectable()
class ShiftAssignmentDetailRepository extends BaseRepository<ShiftAssignmentDetailDocument> {
  constructor() {
    super(ShiftAssignmentDetailModel);
  }

  /**
   * Find shift assignment details by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<ShiftAssignmentDetailDocument[]> {
    return this.model.find({ tenant_id: tenantId }).exec();
  }

  /**
   * Get next code for tenant
   */
  async getNextCode(tenantId: string): Promise<number> {
    const lastRecord = await this.model
      .findOne({ tenant_id: tenantId })
      .sort({ code: -1 })
      .exec();
    
    return lastRecord ? lastRecord.code + 1 : 1;
  }
}

export default ShiftAssignmentDetailRepository;
