import { Repository, Injectable } from '@c-cam/core';
import { ShiftAssignmentDocument } from '@/database/entities/ShiftAssignmentModel';
import ShiftAssignmentModel from '@/database/entities/ShiftAssignmentModel';

/**
 * Repository for managing shift assignments
 * Extends the Repository with ShiftAssignmentDocument type
 */
@Injectable()
class ShiftAssignmentRepository extends Repository<ShiftAssignmentDocument> {
  constructor() {
    super(ShiftAssignmentModel);
  }

  /**
   * Find shift assignments by shift ID
   */
  async findByShiftId(shiftId: string): Promise<ShiftAssignmentDocument[]> {
    return this.find({ shift_id: shiftId });
  }

  /**
   * Find shift assignments by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<ShiftAssignmentDocument[]> {
    return this.find({ tenant_id: tenantId });
  }
}

export default ShiftAssignmentRepository;
