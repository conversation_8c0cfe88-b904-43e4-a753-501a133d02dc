import { BaseRepository, Injectable } from '@c-cam/core';
import { ShiftAssignmentDocument } from '@/database/entities/ShiftAssignmentModel';
import ShiftAssignmentModel from '@/database/entities/ShiftAssignmentModel';

/**
 * Repository for managing shift assignments
 * Extends the BaseRepository with ShiftAssignmentDocument type
 */
@Injectable()
class ShiftAssignmentRepository extends BaseRepository<ShiftAssignmentDocument> {
  constructor() {
    super(ShiftAssignmentModel);
  }

  /**
   * Find shift assignments by shift ID
   */
  async findByShiftId(shiftId: string): Promise<ShiftAssignmentDocument[]> {
    return this.model.find({ shift_id: shiftId }).exec();
  }

  /**
   * Find shift assignments by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<ShiftAssignmentDocument[]> {
    return this.model.find({ tenant_id: tenantId }).exec();
  }
}

export default ShiftAssignmentRepository;
