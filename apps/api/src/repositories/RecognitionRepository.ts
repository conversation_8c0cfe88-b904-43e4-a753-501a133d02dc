import { Repository, Injectable } from '@c-cam/core';
import { RecognitionDocument } from '@/database/entities/RecognitionModel';
import RecognitionModel from '@/database/entities/RecognitionModel';

/**
 * Repository for managing recognition logs
 * Extends the Repository with RecognitionDocument type
 */
@Injectable()
class RecognitionRepository extends Repository<RecognitionDocument> {
  constructor() {
    super(RecognitionModel);
  }

  /**
   * Find recognition logs by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<RecognitionDocument[]> {
    return this.find({ tenant_id: tenantId });
  }

  /**
   * Find recognition logs by recognize IDs
   */
  async findByRecognizeIds(recognizeIds: string[]): Promise<RecognitionDocument[]> {
    return this.find({ recognize_id: { $in: recognizeIds } });
  }

  /**
   * Find recognition logs by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<RecognitionDocument[]> {
    return this.find({
      updatedAt: {
        $gte: startDate,
        $lte: endDate
      }
    });
  }

  /**
   * Find recognition logs by user mapping and date range
   */
  async findByUserMappingAndDateRange(
    recognizeIds: string[],
    startDate: Date,
    endDate: Date
  ): Promise<RecognitionDocument[]> {
    const query = this.getModel().find({
      recognize_id: { $in: recognizeIds },
      updatedAt: {
        $gte: startDate,
        $lte: endDate
      }
    }).sort({ updatedAt: -1 });

    return query.exec();
  }
}

export default RecognitionRepository;
