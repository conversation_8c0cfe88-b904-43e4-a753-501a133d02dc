import { BaseRepository, Injectable } from '@c-cam/core';
import { TimeOffScheduleDocument } from '@/database/entities/TimeOffScheduleModel';
import TimeOffScheduleModel from '@/database/entities/TimeOffScheduleModel';

/**
 * Repository for managing time off schedules
 * Extends the BaseRepository with TimeOffScheduleDocument type
 */
@Injectable()
class TimeOffScheduleRepository extends BaseRepository<TimeOffScheduleDocument> {
  constructor() {
    super(TimeOffScheduleModel);
  }

  /**
   * Find time off schedules by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<TimeOffScheduleDocument[]> {
    return this.model.find({ tenant_id: { $in: [tenantId] } }).exec();
  }

  /**
   * Find time off schedules by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<TimeOffScheduleDocument[]> {
    return this.model.find({
      $or: [
        { startDate: { $lte: endDate }, endDate: { $gte: startDate } }
      ]
    }).exec();
  }

  /**
   * Find active time off schedules
   */
  async findActive(): Promise<TimeOffScheduleDocument[]> {
    return this.model.find({ status: 'active' }).exec();
  }
}

export default TimeOffScheduleRepository;
