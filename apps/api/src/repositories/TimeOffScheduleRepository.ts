import { Repository, Injectable } from '@c-cam/core';
import { TimeOffScheduleDocument } from '@/database/entities/TimeOffScheduleModel';
import TimeOffScheduleModel from '@/database/entities/TimeOffScheduleModel';

/**
 * Repository for managing time off schedules
 * Extends the Repository with TimeOffScheduleDocument type
 */
@Injectable()
class TimeOffScheduleRepository extends Repository<TimeOffScheduleDocument> {
  constructor() {
    super(TimeOffScheduleModel);
  }

  /**
   * Find time off schedules by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<TimeOffScheduleDocument[]> {
    return this.find({ tenant_id: { $in: [tenantId] } });
  }

  /**
   * Find time off schedules by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<TimeOffScheduleDocument[]> {
    return this.find({
      $or: [
        { startDate: { $lte: endDate }, endDate: { $gte: startDate } }
      ]
    });
  }

  /**
   * Find active time off schedules
   */
  async findActive(): Promise<TimeOffScheduleDocument[]> {
    return this.find({ status: 'active' });
  }
}

export default TimeOffScheduleRepository;
