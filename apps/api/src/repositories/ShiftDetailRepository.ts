import ShiftDetailModel, {
  ShiftDetailDocument,
} from '@/database/entities/ShiftDetailModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing shift details
 * Extends the BaseRepository with ShiftDetailDocument type
 */
@Injectable()
class ShiftDetailRepository extends Repository<ShiftDetailDocument> {
  constructor() {
    super(ShiftDetailModel);
  }

  /**
   * Find shift details by shift ID
   * @param shiftId The shift ID to search for
   * @returns A promise that resolves to an array of shift details
   */
  async findByShiftId(shiftId: string): Promise<ShiftDetailDocument[]> {
    return this.find({ shift_id: shiftId });
  }

  /**
   * Find overnight shift details
   * @returns A promise that resolves to an array of overnight shift details
   */
  async findOvernightShifts(): Promise<ShiftDetailDocument[]> {
    return this.find({ is_overnight: true });
  }

  /**
   * Find shift details by total working hours
   * @param totalWorkingHours The total working hours to search for
   * @returns A promise that resolves to an array of shift details
   */
  async findByTotalWorkingHours(
    totalWorkingHours: number,
  ): Promise<ShiftDetailDocument[]> {
    return this.find({ total_working_hours: totalWorkingHours });
  }

  /**
   * Find shift details by tenant ID
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of shift details
   */
  async findByTenantId(tenantId: string): Promise<ShiftDetailDocument[]> {
    return this.find({ tenant_id: tenantId });
  }

  /**
   * Get next code for tenant
   * @param tenantId The tenant ID
   * @returns A promise that resolves to the next code number
   */
  async getNextCode(tenantId: string): Promise<number> {
    const lastRecord = await this.getModel()
      .findOne({ tenant_id: tenantId })
      .sort({ code: -1 })
      .exec();

    return lastRecord ? lastRecord.code + 1 : 1;
  }

  /**
   * Delete shift details by shift ID
   * @param shiftId The shift ID to delete details for
   * @returns A promise that resolves to true if any details were deleted, false otherwise
   */
  async deleteByShiftId(shiftId: string): Promise<boolean> {
    const result = await this.getModel()
      .deleteMany({ shift_id: shiftId })
      .exec();
    return result.deletedCount > 0;
  }
}

export default ShiftDetailRepository;
