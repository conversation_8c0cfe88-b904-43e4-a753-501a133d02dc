import ShiftDetailModel, {
  ShiftDetailDocument,
} from '@/database/entities/ShiftDetailModel';
import { Repository, Injectable } from '@c-cam/core';

/**
 * Repository for managing shift details
 * Extends the BaseRepository with ShiftDetailDocument type
 */
@Injectable()
class ShiftDetailRepository extends Repository<ShiftDetailDocument> {
  constructor() {
    super(ShiftDetailModel);
  }



  /**
   * Find shift details by tenant ID
   * @param tenantId The tenant ID to search for
   * @returns A promise that resolves to an array of shift details
   */
  async findByTenantId(tenantId: string): Promise<ShiftDetailDocument[]> {
    return this.find({ tenant_id: tenantId });
  }

  /**
   * Get next code for tenant
   * @param tenantId The tenant ID
   * @returns A promise that resolves to the next code number
   */
  async getNextCode(tenantId: string): Promise<number> {
    const lastRecord = await this.getModel()
      .findOne({ tenant_id: tenantId })
      .sort({ code: -1 })
      .exec();

    return lastRecord ? lastRecord.code + 1 : 1;
  }


}

export default ShiftDetailRepository;
