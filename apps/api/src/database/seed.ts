/**
 * Database Seeding Logic
 * Seeds base system data for c-cam application
 */

import { MongooseConnection } from '@c-cam/core';
import { logger } from '@c-cam/logger';
import { SEED_CONFIGURATION, SYSTEM_CONSTANTS } from '@c-cam/shared';
import { UserGender, UserStatus } from '@c-cam/types';

// Import all models
import MemberRoleModel from './entities/MemberRoleModel';
import PermissionModel from './entities/PermissionModel';
import PolicyModel from './entities/PolicyModel';
import RecognitionModel from './entities/RecognitionModel';
import RoleModel from './entities/RoleModel';
import ShiftAssignmentDetailModel from './entities/ShiftAssignmentDetailModel';
import ShiftAssignmentModel from './entities/ShiftAssignmentModel';
import ShiftDetailModel from './entities/ShiftDetailModel';
import ShiftModel from './entities/ShiftModel';
import TenantModel from './entities/TenantModel';
import TimeOffScheduleModel from './entities/TimeOffScheduleModel';
import UnitModel from './entities/UnitModel';
import UsersModel from './entities/UsersModel';

/**
 * Interface for seeding options
 */
export interface SeedOptions {
  /**
   * Whether to force recreate existing data
   */
  force?: boolean;

  /**
   * Whether to seed only specific entities
   */
  entities?: string[];

  /**
   * Custom admin user data
   */
  adminUser?: {
    username?: string;
    password?: string;
    email?: string;
    name?: string;
  };
}

// System constants are now imported from @c-cam/shared package

/**
 * Main seeding class
 */
export class DatabaseSeeder {
  private seededData: {
    tenants: any[];
    units: any[];
    roles: any[];
    permissions: any[];
    users: any[];
    policies: any[];
    shifts: any[];
  } = {
    tenants: [],
    units: [],
    roles: [],
    permissions: [],
    users: [],
    policies: [],
    shifts: [],
  };

  /**
   * Run the complete seeding process
   */
  public async seed(options: SeedOptions = {}): Promise<void> {
    try {
      const startTime = Date.now();
      logger.info('Starting database seeding process...');

      // Check if database connection is available
      const mongoConnection = MongooseConnection.getInstance();
      if (!mongoConnection.isConnected()) {
        throw new Error(
          'Database connection is not available. Please ensure MongoDB is connected.',
        );
      }

      // Clear existing data if force option is enabled
      if (options.force) {
        await this.clearExistingData();
      }

      // Phase 1: Seed tenants first (required for units)
      logger.info('Phase 1: Seeding tenants...');
      const phase1StartTime = Date.now();
      await this.seedTenants();
      const phase1Duration = Date.now() - phase1StartTime;
      logger.info(`Phase 1 completed in ${phase1Duration}ms`);

      // Phase 1.5: Seed entities that depend on tenants and other independent entities
      logger.info('Phase 1.5: Seeding tenant-dependent and independent entities in parallel...');
      const phase1_5StartTime = Date.now();
      await Promise.all([
        this.seedUnits(),
        this.seedRoles(),
        this.seedPolicies(),
        this.seedShifts(),
      ]);
      const phase1_5Duration = Date.now() - phase1_5StartTime;
      logger.info(`Phase 1.5 completed in ${phase1_5Duration}ms`);

      // Phase 2: Seed entities that depend on roles
      logger.info('Phase 2: Seeding role-dependent entities...');
      const phase2StartTime = Date.now();
      await this.seedPermissions();
      const phase2Duration = Date.now() - phase2StartTime;
      logger.info(`Phase 2 completed in ${phase2Duration}ms`);

      // Phase 3: Seed users (depends on units) and member roles (depends on users and roles)
      logger.info('Phase 3: Seeding users and member roles...');
      const phase3StartTime = Date.now();
      await this.seedUsers(options.adminUser);
      await this.seedMemberRoles();
      const phase3Duration = Date.now() - phase3StartTime;
      logger.info(`Phase 3 completed in ${phase3Duration}ms`);

      const totalDuration = Date.now() - startTime;
      logger.info(`Database seeding completed successfully in ${totalDuration}ms!`);
      this.logSeedingSummary();
    } catch (error) {
      logger.error('Database seeding failed:', error);
      throw error;
    }
  }

  /**
   * Clear existing data from all collections
   */
  private async clearExistingData(): Promise<void> {
    logger.info('Clearing existing data...');

    try {
      await Promise.all([
        MemberRoleModel.deleteMany({}),
        UsersModel.deleteMany({}),
        PermissionModel.deleteMany({}),
        PolicyModel.deleteMany({}),
        RecognitionModel.deleteMany({}),
        RoleModel.deleteMany({}),
        ShiftAssignmentDetailModel.deleteMany({}),
        ShiftAssignmentModel.deleteMany({}),
        ShiftDetailModel.deleteMany({}),
        ShiftModel.deleteMany({}),
        TenantModel.deleteMany({}),
        TimeOffScheduleModel.deleteMany({}),
        UnitModel.deleteMany({}),
      ]);

      logger.info('Existing data cleared successfully');
    } catch (error) {
      logger.error('Failed to clear existing data:', error);
      throw error;
    }
  }

  /**
   * Seed default tenants
   */
  private async seedTenants(): Promise<void> {
    logger.info('Seeding tenants...');

    try {
      const existingTenant = await TenantModel.findOne({
        name: SYSTEM_CONSTANTS.DEFAULT_TENANT.name,
      });

      if (!existingTenant) {
        const tenant = await TenantModel.create({
          name: SYSTEM_CONSTANTS.DEFAULT_TENANT.name,
          address: SYSTEM_CONSTANTS.DEFAULT_TENANT.address,
          created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
        });

        this.seededData.tenants.push(tenant);
        logger.info(`Created default tenant: ${tenant.name}`);
      } else {
        this.seededData.tenants.push(existingTenant);
        logger.info('Default tenant already exists');
      }
    } catch (error) {
      logger.error('Failed to seed tenants:', error);
      throw error;
    }
  }

  /**
   * Seed default organizational units
   */
  private async seedUnits(): Promise<void> {
    logger.info('Seeding organizational units...');

    try {
      // Get the default tenant that was seeded in Phase 1
      const defaultTenant = this.seededData.tenants[0];
      if (!defaultTenant) {
        throw new Error('Default tenant not found. Cannot create units without a tenant.');
      }

      const existingUnit = await UnitModel.findOne({
        name: SYSTEM_CONSTANTS.DEFAULT_UNIT.name,
      });

      if (!existingUnit) {
        const unit = await UnitModel.create({
          name: SYSTEM_CONSTANTS.DEFAULT_UNIT.name,
          organization_id: (defaultTenant as any)._id.toString(), // Use actual tenant ID
          created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
        });

        this.seededData.units.push(unit);
        logger.info(`Created default unit: ${unit.name} with organization_id: ${(defaultTenant as any)._id.toString()}`);
      } else {
        this.seededData.units.push(existingUnit);
        logger.info('Default unit already exists');
      }
    } catch (error) {
      logger.error('Failed to seed units:', error);
      throw error;
    }
  }

  /**
   * Seed system roles from shared configuration
   */
  private async seedRoles(): Promise<void> {
    logger.info('Seeding system roles...');

    try {
      // Create role seeding promises for parallel execution
      const rolePromises = SEED_CONFIGURATION.roles.map(async (roleConfig) => {
        const existingRole = await RoleModel.findOne({ name: roleConfig.name });

        if (!existingRole) {
          const role = await RoleModel.create({
            name: roleConfig.name,
            description: roleConfig.description,
            created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
          });

          logger.info(`Created role: ${roleConfig.name}`);
          return role;
        } else {
          logger.info(`Role already exists: ${roleConfig.name}`);
          return existingRole;
        }
      });

      // Execute all role creation operations in parallel
      const roles = await Promise.all(rolePromises);
      this.seededData.roles.push(...roles);

      logger.info(`Seeded ${roles.length} roles in parallel`);
    } catch (error) {
      logger.error('Failed to seed roles:', error);
      throw error;
    }
  }

  /**
   * Seed system permissions
   */
  private async seedPermissions(): Promise<void> {
    logger.info('Seeding system permissions...');

    try {
      // Get all roles for permission assignment
      const roles = await RoleModel.find({});
      const roleMap = new Map(roles.map((role) => [role.name, (role as any)._id.toString()]));

      // Define permission matrix for each role
      const permissionMatrix = this.getPermissionMatrix();

      // Create permission seeding promises for parallel execution
      const rolePermissionPromises = Object.entries(permissionMatrix).map(
        async ([roleName, permissions]) => {
          const roleId = roleMap.get(roleName);
          if (!roleId) {
            logger.warn(`Role not found: ${roleName}`);
            return [];
          }

          // Create promises for all permissions of this role
          const permissionPromises = permissions.map(async (permission) => {
            const existingPermission = await PermissionModel.findOne({
              role_id: roleId,
              module: permission.module,
              feature: permission.feature,
              action: permission.action,
            });

            if (!existingPermission) {
              const newPermission = await PermissionModel.create({
                role_id: roleId,
                module: permission.module,
                feature: permission.feature,
                action: permission.action,
                created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
              });

              logger.info(
                `Created permission: ${roleName} - ${permission.module}.${permission.feature}.${permission.action}`,
              );
              return newPermission;
            } else {
              return existingPermission;
            }
          });

          // Execute all permissions for this role in parallel
          return Promise.all(permissionPromises);
        },
      );

      // Execute all role permission operations in parallel and flatten results
      const rolePermissionResults = await Promise.all(rolePermissionPromises);
      const allPermissions = rolePermissionResults.flat();
      this.seededData.permissions.push(...allPermissions);

      logger.info(
        `Seeded ${allPermissions.length} permissions across ${rolePermissionResults.length} roles in parallel`,
      );
    } catch (error) {
      logger.error('Failed to seed permissions:', error);
      throw error;
    }
  }

  /**
   * Get permission matrix for roles from shared configuration
   */
  private getPermissionMatrix() {
    return SEED_CONFIGURATION.permissions;
  }

  /**
   * Seed system policies from shared configuration
   */
  private async seedPolicies(): Promise<void> {
    logger.info('Seeding system policies...');

    try {
      const defaultPolicies = SEED_CONFIGURATION.policies;

      // Create policy seeding promises for parallel execution
      const policyPromises = defaultPolicies.map(async (policyData) => {
        try {
          const existingPolicy = await PolicyModel.findOne({ name: policyData.name });

          if (!existingPolicy) {
            // Prepare policy data with proper field mapping
            const policyToCreate = {
              name: policyData.name,
              description: policyData.description,
              type: policyData.type,
              conditions: policyData.conditions,
              resources: policyData.resources,
              actions: policyData.actions,
              effect: policyData.effect,
              priority: policyData.priority,
              isActive: policyData.is_active ?? true, // Map is_active to isActive
              metadata: policyData.metadata,
              createdBy: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
            };

            const policy = await PolicyModel.create(policyToCreate);
            logger.info(`Created policy: ${policy.name}`);
            return policy;
          } else {
            logger.info(`Policy already exists: ${policyData.name}`);
            return existingPolicy;
          }
        } catch (error) {
          logger.error(`Failed to create policy ${policyData.name}:`, error);
          throw error;
        }
      });

      // Execute all policy creation operations in parallel
      const policies = await Promise.all(policyPromises);
      this.seededData.policies.push(...policies);

      logger.info(`Seeded ${policies.length} policies in parallel`);

      // Load policies into PolicyRegistry after seeding
      await this.loadPoliciesIntoRegistry();
    } catch (error) {
      logger.error('Failed to seed policies:', error);
      throw error;
    }
  }

  /**
   * Load all policies from database into PolicyRegistry
   */
  private async loadPoliciesIntoRegistry(): Promise<void> {
    try {
      const { PolicyRegistryService } = await import('../services/PolicyRegistryService.js');
      const policyRegistryService = new PolicyRegistryService();
      await policyRegistryService.loadPoliciesIntoRegistry();
    } catch (error) {
      logger.error('Failed to load policies into registry:', error);
      throw error;
    }
  }

  /**
   * Seed default shifts
   */
  private async seedShifts(): Promise<void> {
    logger.info('Seeding default shifts...');

    try {
      // Create shift seeding promises for parallel execution
      const shiftPromises = SYSTEM_CONSTANTS.SHIFTS.map(async (shiftData) => {
        const existingShift = await ShiftModel.findOne({ name: shiftData.name });

        if (!existingShift) {
          const shift = await ShiftModel.create(shiftData);
          logger.info(`Created shift: ${shift.name}`);
          return shift;
        } else {
          logger.info(`Shift already exists: ${shiftData.name}`);
          return existingShift;
        }
      });

      // Execute all shift creation operations in parallel
      const shifts = await Promise.all(shiftPromises);
      this.seededData.shifts.push(...shifts);

      logger.info(`Seeded ${shifts.length} shifts in parallel`);
    } catch (error) {
      logger.error('Failed to seed shifts:', error);
      throw error;
    }
  }

  /**
   * Seed default admin user
   */
  private async seedUsers(adminUserData?: SeedOptions['adminUser']): Promise<void> {
    logger.info('Seeding default users...');

    try {
      // Get default unit for user assignment
      const defaultUnit = this.seededData.units[0];
      if (!defaultUnit) {
        throw new Error('Default unit not found. Cannot create users.');
      }

      // Default admin user configuration from shared config
      const defaultAdminConfig = {
        ...SEED_CONFIGURATION.default_admin,
        ...adminUserData,
      };

      const existingAdmin = await UsersModel.findOne({
        username: defaultAdminConfig.username,
      });

      if (!existingAdmin) {
        // Hash password using bcrypt
        const bcrypt = await import('bcrypt');
        const hashedPassword = await bcrypt.hash(defaultAdminConfig.password || 'admin123', 10);

        const adminUser = await UsersModel.create({
          unit_id: (defaultUnit as any)._id.toString(),
          code: 'ADMIN001',
          name: defaultAdminConfig.name,
          email: defaultAdminConfig.email,
          username: defaultAdminConfig.username,
          password: hashedPassword,
          gender: UserGender.OTHER,
          status: UserStatus.ACTIVE,
          created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
        });

        this.seededData.users.push(adminUser);
        logger.info(`Created admin user: ${adminUser.username}`);
        logger.info(
          `Admin password: ${defaultAdminConfig.password} (Please change after first login)`,
        );
      } else {
        this.seededData.users.push(existingAdmin);
        logger.info('Admin user already exists');
      }
    } catch (error) {
      logger.error('Failed to seed users:', error);
      throw error;
    }
  }

  /**
   * Seed member roles (assign roles to users)
   */
  private async seedMemberRoles(): Promise<void> {
    logger.info('Seeding member roles...');

    try {
      // Get admin user and super admin role
      const adminUser = this.seededData.users.find((user) => user.username === 'admin');
      const superAdminRole = this.seededData.roles.find(
        (role) => role.name === SYSTEM_CONSTANTS.ROLES.SUPER_ADMIN,
      );

      if (!adminUser || !superAdminRole) {
        logger.warn('Admin user or Super Admin role not found. Skipping member role assignment.');
        return;
      }

      const existingMemberRole = await MemberRoleModel.findOne({
        user_id: (adminUser as any)._id.toString(),
        role_id: (superAdminRole as any)._id.toString(),
      });

      if (!existingMemberRole) {
        const memberRole = await MemberRoleModel.create({
          user_id: (adminUser as any)._id.toString(),
          role_id: (superAdminRole as any)._id.toString(),
          created_by: SYSTEM_CONSTANTS.SYSTEM_USER_ID,
        });

        // Note: We don't have a memberRoles array in seededData, so we'll just log it
        logger.info(`Member role created with ID: ${(memberRole as any)._id}`);
        logger.info(`Assigned Super Admin role to admin user`);
      } else {
        logger.info('Admin user already has Super Admin role');
      }
    } catch (error) {
      logger.error('Failed to seed member roles:', error);
      throw error;
    }
  }

  /**
   * Log seeding summary
   */
  private logSeedingSummary(): void {
    logger.info('=== Database Seeding Summary ===');
    logger.info(`Tenants: ${this.seededData.tenants.length}`);
    logger.info(`Units: ${this.seededData.units.length}`);
    logger.info(`Roles: ${this.seededData.roles.length}`);
    logger.info(`Permissions: ${this.seededData.permissions.length}`);
    logger.info(`Policies: ${this.seededData.policies.length}`);
    logger.info(`Shifts: ${this.seededData.shifts.length}`);
    logger.info(`Users: ${this.seededData.users.length}`);
    logger.info('================================');
  }
}

/**
 * Convenience function to run seeding
 */
export async function runSeeding(options: SeedOptions = {}): Promise<void> {
  const seeder = new DatabaseSeeder();
  await seeder.seed(options);
}

/**
 * Export default seeder instance
 */
export default new DatabaseSeeder();
