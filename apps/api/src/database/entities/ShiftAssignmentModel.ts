import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { ShiftAssignmentAttributes, DaysOfWeek } from '@c-cam/types';

/**
 * Shift Assignment Document Interface
 * Extends the ShiftAssignmentAttributes (excluding _id) and Document
 */
export interface ShiftAssignmentDocument
  extends Omit<ShiftAssignmentAttributes, '_id'>,
    Document {}

/**
 * Shift Assignment Schema
 * Defines the MongoDB schema for shift assignments
 */
const ShiftAssignmentSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  shift_id: {
    type: String,
    ref: 'shift',
    required: true,
  },
  days_of_week: [{
    type: String,
    enum: Object.values(DaysOfWeek),
  }],
  days: [{ type: Date }],
});

// Add indexes
ShiftAssignmentSchema.index({ tenant_id: 1, shift_id: 1 });

// Create and export the model
const ShiftAssignmentModel = createModel<ShiftAssignmentDocument>(
  'shift_assignment',
  ShiftAssignmentSchema,
);

export default ShiftAssignmentModel;
