import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { Recognition } from '@c-cam/types';

/**
 * Recognition Document Interface
 * Extends the Recognition (excluding _id) and Document
 */
export interface RecognitionDocument
  extends Omit<Recognition, '_id'>,
    Document {}

/**
 * Recognition Schema
 * Defines the MongoDB schema for recognition logs
 */
const RecognitionSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  detected_face_file_id: {
    type: String,
    ref: 'file',
    required: false,
  },
  avatar: { type: String, required: true },
  channel_id: { type: String, required: true },
  company: { type: String, required: true },
  detected_face: { type: String, required: true }, // ảnh base64
  device_id: { type: String, required: true }, // id địa chỉ box
  edge_device_type: { type: String, required: true },
  event_id: { type: String, required: true },
  face_reid: { type: String, required: true },
  job: { type: String, required: true },
  mask: { type: String, required: true },
  messageid: { type: String, required: true },
  open_acs: { type: String, required: true },
  recognize_id: { type: String, required: true },
  recognize_name: { type: String, required: true },
  recognize_status: { type: String, required: true },
  src_id: { type: String, required: true },
  temperature: { type: String, required: true },
  tracking_id: { type: String, required: true },
  updatedAt: { type: Date, required: true }, // time ghi nhận
});

// Add indexes
RecognitionSchema.index({ tenant_id: 1, recognize_id: 1 });
RecognitionSchema.index({ tenant_id: 1, updatedAt: -1 });
RecognitionSchema.index({ recognize_id: 1, updatedAt: -1 });

// Create and export the model
const RecognitionModel = createModel<RecognitionDocument>(
  'recognition',
  RecognitionSchema,
);

export default RecognitionModel;
