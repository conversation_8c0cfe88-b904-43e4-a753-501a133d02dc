import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { 
  TimeOffScheduleAttributes, 
  DayOffType, 
  EntityType,
  Status 
} from '@c-cam/types';

/**
 * Time Off Schedule Document Interface
 * Extends the TimeOffScheduleAttributes (excluding _id) and Document
 */
export interface TimeOffScheduleDocument
  extends Omit<TimeOffScheduleAttributes, '_id'>,
    Document {}

/**
 * Time Off Schedule Schema
 * Defines the MongoDB schema for time off schedules
 */
const TimeOffScheduleSchema = createSchema({
  tenant_id: [{
    type: String,
    ref: 'tenant',
    required: true,
  }],
  name: { type: String, required: true },
  type: {
    type: String,
    enum: Object.values(DayOffType),
    required: true,
  },
  entity_type: {
    type: String,
    enum: Object.values(EntityType),
    required: true,
  },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  status: {
    type: String,
    enum: Object.values(Status),
    default: Status.Active,
    required: true,
  },
  targets: [{ type: String }],
  excludedTargets: [{ type: String }],
  createdBy: { type: String, required: true },
});

// Add indexes
TimeOffScheduleSchema.index({ tenant_id: 1, name: 1 });
TimeOffScheduleSchema.index({ startDate: 1, endDate: 1 });

// Create and export the model
const TimeOffScheduleModel = createModel<TimeOffScheduleDocument>(
  'time_off_schedule',
  TimeOffScheduleSchema,
);

export default TimeOffScheduleModel;
