import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import { ShiftAttributes, ShiftType } from '@c-cam/types';

/**
 * Shift Document Interface
 * Extends the ShiftAttributes (excluding _id) and Document
 */
export interface ShiftDocument extends Omit<ShiftAttributes, '_id'>, Document {}

/**
 * Shift Schema
 * Defines the MongoDB schema for shifts
 */
const ShiftSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  shift_detail_id: {
    type: String,
    ref: 'shift_detail',
    required: true,
  },
  code: { type: Number, required: true },
  name: { type: String, required: true },
  type: {
    type: String,
    enum: Object.values(ShiftType),
    required: true,
  },
  work_coefficient: { type: Number, required: true, default: 1 },
  created_by: { type: String, required: true },
});

// Add indexes
ShiftSchema.index({ tenant_id: 1, code: 1 }, { unique: true });
ShiftSchema.index({ tenant_id: 1, name: 1 }, { unique: true });

// Create and export the model
const ShiftModel = createModel<ShiftDocument>('shift', ShiftSchema);

export default ShiftModel;
