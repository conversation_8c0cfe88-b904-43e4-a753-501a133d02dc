import { Document } from 'mongoose';
import { createSchema, createModel } from '@c-cam/core';
import {
  ShiftDetailAttributes,
  WorkShiftType,
  EntityType,
  ShiftAssignmentType,
  DateRangeType,
  Status
} from '@c-cam/types';

/**
 * Shift Detail Document Interface
 * Extends the ShiftDetailAttributes (excluding _id) and Document
 */
export interface ShiftDetailDocument
  extends Omit<ShiftDetailAttributes, '_id'>,
    Document {}

/**
 * Shift Detail Schema
 * Defines the MongoDB schema for shift details
 */
const ShiftDetailSchema = createSchema({
  tenant_id: {
    type: String,
    ref: 'tenant',
    required: true,
  },
  shift_assignment: [{
    type: String,
    ref: 'shift_assignment',
  }],
  code: { type: Number, required: true },
  name: { type: String, required: true },
  work_shift_type: {
    type: String,
    enum: Object.values(WorkShiftType),
    required: true,
  },
  entity_type: {
    type: String,
    enum: Object.values(EntityType),
    required: true,
  },
  assignment_type: {
    type: String,
    enum: Object.values(ShiftAssignmentType),
    required: true,
  },
  dateRange_type: {
    type: String,
    enum: Object.values(DateRangeType),
    required: true,
  },
  start_date: { type: Date, required: true },
  end_date: { type: Date, required: true },
  status: {
    type: String,
    enum: Object.values(Status),
    default: Status.Active,
    required: true,
  },
  created_by: { type: String, required: true },
  targets: [{ type: String }],
  excluded_targets: [{ type: String }],
});

// Add indexes
ShiftDetailSchema.index({ tenant_id: 1, code: 1 }, { unique: true });
ShiftDetailSchema.index({ tenant_id: 1, name: 1 }, { unique: true });

// Create and export the model
const ShiftDetailModel = createModel<ShiftDetailDocument>(
  'shift_detail',
  ShiftDetailSchema,
);

export default ShiftDetailModel;
