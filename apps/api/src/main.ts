/**
 * CCamAI API Application
 * Entry point for the API server with database seeding
 */

// Initialize tracing FIRST before any other imports
import { initializeTracing } from './tracing.js';

import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';

import {
  Container,
  GlobalAuthorizedMiddleware,
  MongooseConnection,
  RouterFactory
} from '@c-cam/core';
import { logger as smartLogger } from '@c-cam/logger';

import { environment } from './configs/environment';
import { DatabaseSeeder, runSeeding } from './database/seed';

// Controllers
import { CameraController } from './controllers/camera.controller';
import { DailyAttendanceSummariesController } from './controllers/daily-attendance-summaries.controller';
import { EdgeDeviceInfoController } from './controllers/edge-device-info.controller';
import { EdgeDeviceLogsController } from './controllers/edge-device-logs.controller';
import { EdgeDeviceController } from './controllers/edge-device.controller';
import { FaceImagesController } from './controllers/face-images.controller';
import { FaceRecognitionLogsController } from './controllers/face-recognition-logs.controller';
import { IdentityController } from './controllers/identity.controller';
import { MemberRoleController } from './controllers/member-role.controller';
import { PermissionController } from './controllers/permission.controller';
import { RecognitionController } from './controllers/recognition.controller';
import { RoleController } from './controllers/role.controller';
import { ShiftAssignmentController } from './controllers/shift-assignment.controller';
import { ShiftAssignmentDetailController } from './controllers/shift-assignment-detail.controller';
import { ShiftDetailController } from './controllers/shift-detail.controller';
import { ShiftController } from './controllers/shift.controller';
import { StorageController } from './controllers/storage.controller';
import { TenantController } from './controllers/tenant.controller';
import { TimeOffScheduleController } from './controllers/time-off-schedule.controller';
import { TracesController } from './controllers/traces.controller';
import { UnitController } from './controllers/unit.controller';
import { UsersController } from './controllers/users.controller';
import { setupTracingMiddlewares } from './middlewares/tracing.middleware.js';

// Initialize Hono app
const app = new Hono();

// Middleware
app.use(
  '*',
  cors({
    origin: environment.cors.origin.split(',').map((origin) => origin.trim()),
    allowHeaders: [
      'Authorization',
      'Content-Type',
      'Accept',
      'Origin',
      'X-Requested-With',
      'Access-Control-Request-Method',
      'Access-Control-Request-Headers',
      'X-Trace-Id',
      'X-Span-Id',
      'X-Correlation-Id',
    ],
    allowMethods: environment.cors.methods,
    credentials: true,
    maxAge: 86400, // Cache preflight for 24 hours
    exposeHeaders: ['Authorization', 'X-Trace-Id', 'X-Span-Id', 'X-Correlation-Id'],
  }),
);

// Add tracing middlewares
setupTracingMiddlewares().forEach((middleware) => {
  app.use('*', middleware);
});

app.use('*', logger());
app.use('*', GlobalAuthorizedMiddleware);

// Register controllers
const controllers = RouterFactory.createRoutes(() => {
  Container.register(DatabaseSeeder);
  Container.register(IdentityController);
  Container.register(CameraController);
  Container.register(DailyAttendanceSummariesController);
  Container.register(EdgeDeviceController);
  Container.register(EdgeDeviceInfoController);
  Container.register(EdgeDeviceLogsController);
  Container.register(FaceImagesController);
  Container.register(FaceRecognitionLogsController);
  Container.register(MemberRoleController);
  Container.register(PermissionController);
  Container.register(RecognitionController);
  Container.register(RoleController);
  Container.register(ShiftAssignmentController);
  Container.register(ShiftAssignmentDetailController);
  Container.register(ShiftController);
  Container.register(ShiftDetailController);
  Container.register(TenantController);
  Container.register(TimeOffScheduleController);
  Container.register(TracesController);
  Container.register(UnitController);
  Container.register(UsersController);
  Container.register(StorageController);
});

// Routes
app.route('/', controllers);
app.get('/health', (c) => c.json({ message: 'OK' }));

// Add error handler using Hono's onError method
app.onError((error, c) => {
  console.log('=== HONO ERROR HANDLER ===');
  console.log('Error:', error);
  console.log('Error name:', error?.name);
  console.log('Error message:', error?.message);
  console.log('Error statusCode:', (error as any)?.statusCode);
  console.log('Error code:', (error as any)?.code);
  console.log('Is HttpError:', error instanceof Error);
  console.log('========================');

  // Handle UnauthorizedError specifically
  if ((error as any)?.statusCode === 401 || (error as any)?.code === 'UNAUTHORIZED') {
    return c.json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: error.message || 'Invalid credentials'
      }
    }, 401);
  }

  // Handle other HTTP errors
  if ((error as any)?.statusCode) {
    return c.json({
      success: false,
      error: {
        code: (error as any)?.code || 'HTTP_ERROR',
        message: error.message || 'An error occurred'
      }
    }, (error as any).statusCode);
  }

  // Handle generic errors
  return c.json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: environment.name === 'PRODUCTION' ? 'An unexpected error occurred' : error.message
    }
  }, 500);
});

// Database initialization
async function initializeDatabase(): Promise<void> {
  smartLogger.info('Connecting to MongoDB...');
  const mongoConnection = MongooseConnection.getInstance();
  await mongoConnection.initialize({
    uri: environment.mongo.uri,
    options: environment.mongo.options,
  });
  smartLogger.info('MongoDB connected successfully');

  smartLogger.info('Running database seeding...');
  await runSeeding({
    force: false, // Set to true to clear existing data and reseed (temporary fix for duplicates)
    adminUser: {
      username: environment.admin.email.split('@')[0] || 'admin',
      email: environment.admin.email,
      password: environment.admin.password,
      name: 'System Administrator',
    },
  });
  smartLogger.info('Database seeding completed');

  // Load policies into PolicyRegistry
  smartLogger.info('Loading policies into PolicyRegistry...');
  try {
    const { PolicyRegistryService } = await import('./services/PolicyRegistryService.js');
    const policyRegistryService = new PolicyRegistryService();
    await policyRegistryService.loadPoliciesIntoRegistry();
    smartLogger.info('PolicyRegistry loaded successfully');
  } catch (error) {
    smartLogger.error('Failed to load PolicyRegistry:', error);
    // Don't throw error here, let the app continue but log the issue
  }
}

// Server startup
let server: any;

async function startServer(): Promise<void> {
  try {
    smartLogger.info('Initializing CCamAI API...');

    // Initialize tracing first
    await initializeTracing();

    await initializeDatabase();

    smartLogger.info(`Starting server on port ${environment.server.port}...`);
    server = serve({
      fetch: app.fetch,
      port: environment.server.port,
    });
    smartLogger.info(`Server started successfully on port ${environment.server.port}`);
  } catch (error) {
    smartLogger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
const shutdown = async () => {
  smartLogger.info('Shutting down server...');
  try {
    if (server) {
      await new Promise<void>((resolve) => server.close(() => resolve()));
      smartLogger.info('Server closed');
    }

    const mongoConnection = MongooseConnection.getInstance();
    if (mongoConnection.isConnected()) {
      await mongoConnection.close();
      smartLogger.info('Database closed');
    }

    smartLogger.info('Shutdown completed');
    process.exit(0);
  } catch (error) {
    smartLogger.error('Shutdown error:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

export default app;
