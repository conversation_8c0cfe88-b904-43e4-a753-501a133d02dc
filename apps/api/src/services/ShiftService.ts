import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { ShiftDocument } from '@/database/entities/ShiftModel';
import ShiftRepository from '@/repositories/ShiftRepository';

/**
 * Service for managing shifts
 * Extends the BaseModel with ShiftDocument type
 */
@Injectable()
class ShiftService extends BaseModel<ShiftDocument> {
  /**
   * Create a new ShiftService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(ShiftRepository)
    repository: ShiftRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new shift
   * @param shiftData The shift data
   * @returns The newly created shift
   */
  async createShift(shiftData: {
    name: string;
    shift_type: string;
    work_coefficient: number;
  }): Promise<ShiftDocument> {
    // Check if a shift with the same name already exists
    const existingShift = await (this.repository as ShiftRepository).findByName(
      shiftData.name,
    );

    if (existingShift) {
      throw new Error(`Shift with name '${shiftData.name}' already exists`);
    }

    // Validate work coefficient
    if (shiftData.work_coefficient <= 0) {
      throw new Error('Work coefficient must be greater than 0');
    }

    // Create the new shift
    return this.create(shiftData);
  }

  /**
   * Update a shift
   * @param id The shift ID
   * @param shiftData The data to update
   * @returns True if the shift was updated, false otherwise
   */
  async updateShift(
    id: string,
    shiftData: Partial<{
      name: string;
      shift_type: string;
      work_coefficient: number;
    }>,
  ): Promise<boolean> {
    // Check if the shift exists
    const shift = await this.findById(id);
    if (!shift) {
      throw new Error(`Shift with ID '${id}' not found`);
    }

    // If name is being updated, check for duplicates
    if (shiftData.name && shiftData.name !== shift.name) {
      const existingShift = await (
        this.repository as ShiftRepository
      ).findByName(shiftData.name);

      if (existingShift && existingShift.id !== id) {
        throw new Error(`Shift with name '${shiftData.name}' already exists`);
      }
    }

    // Validate work coefficient if provided
    if (
      shiftData.work_coefficient !== undefined &&
      shiftData.work_coefficient <= 0
    ) {
      throw new Error('Work coefficient must be greater than 0');
    }

    // Update the shift
    return this.update(id, shiftData);
  }

  /**
   * Find a shift by name
   * @param name The shift name
   * @returns The shift or null if not found
   */
  async findByName(name: string): Promise<ShiftDocument | null> {
    return (this.repository as ShiftRepository).findByName(name);
  }

  /**
   * Find shifts by shift type
   * @param shiftType The shift type
   * @returns An array of shifts
   */
  async findByShiftType(shiftType: string): Promise<ShiftDocument[]> {
    return (this.repository as ShiftRepository).findByShiftType(shiftType);
  }

  /**
   * Find shifts by work coefficient
   * @param workCoefficient The work coefficient
   * @returns An array of shifts
   */
  async findByWorkCoefficient(
    workCoefficient: number,
  ): Promise<ShiftDocument[]> {
    return (this.repository as ShiftRepository).findByWorkCoefficient(
      workCoefficient,
    );
  }

  /**
   * Find shifts by tenant ID
   * @param tenantId The tenant ID
   * @returns An array of shifts
   */
  async findByTenantId(tenantId: string): Promise<ShiftDocument[]> {
    return (this.repository as ShiftRepository).findByTenantId(tenantId);
  }

  /**
   * Get next code for tenant
   * @param tenantId The tenant ID
   * @returns The next code number
   */
  async getNextCode(tenantId: string): Promise<number> {
    return (this.repository as ShiftRepository).getNextCode(tenantId);
  }

  /**
   * Create shift with auto-generated code
   * @param shiftData The shift data
   * @returns The newly created shift
   */
  async createShiftWithAutoCode(shiftData: Partial<ShiftDocument>): Promise<ShiftDocument> {
    if (!shiftData.tenant_id) {
      throw new Error('tenant_id is required');
    }

    const nextCode = await this.getNextCode(shiftData.tenant_id);
    const createData = { ...shiftData, code: nextCode };

    return this.create(createData);
  }
}

export default ShiftService;
