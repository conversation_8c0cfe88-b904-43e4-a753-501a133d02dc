import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { ShiftAssignmentDocument } from '@/database/entities/ShiftAssignmentModel';
import ShiftAssignmentRepository from '@/repositories/ShiftAssignmentRepository';

/**
 * Service for managing shift assignments
 * Extends the BaseModel with ShiftAssignmentDocument type
 */
@Injectable()
class ShiftAssignmentService extends BaseModel<ShiftAssignmentDocument> {
  /**
   * Create a new ShiftAssignmentService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(ShiftAssignmentRepository)
    private readonly shiftAssignmentRepository: ShiftAssignmentRepository,
  ) {
    super(shiftAssignmentRepository);
  }

  /**
   * Find shift assignments by shift ID
   */
  async findByShiftId(shiftId: string): Promise<ShiftAssignmentDocument[]> {
    return this.shiftAssignmentRepository.findByShiftId(shiftId);
  }

  /**
   * Find shift assignments by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<ShiftAssignmentDocument[]> {
    return this.shiftAssignmentRepository.findByTenantId(tenantId);
  }

  /**
   * Find all shift assignments
   */
  async findAll(): Promise<ShiftAssignmentDocument[]> {
    return this.find();
  }
}

export default ShiftAssignmentService;
