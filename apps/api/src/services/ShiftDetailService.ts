import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { ShiftDetailDocument } from '@/database/entities/ShiftDetailModel';
import ShiftDetailRepository from '@/repositories/ShiftDetailRepository';

/**
 * Service for managing shift details
 * Extends the BaseModel with ShiftDetailDocument type
 */
@Injectable()
class ShiftDetailService extends BaseModel<ShiftDetailDocument> {
  /**
   * Create a new ShiftDetailService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(ShiftDetailRepository)
    repository: ShiftDetailRepository,
  ) {
    super(repository);
  }

  /**
   * Create a new shift detail
   * @param shiftDetailData The shift detail data
   * @returns The newly created shift detail
   */
  async createShiftDetail(shiftDetailData: Partial<ShiftDetailDocument>): Promise<ShiftDetailDocument> {
    // Validate total working hours
    if (shiftDetailData.total_working_hours <= 0) {
      throw new Error('Total working hours must be greater than 0');
    }

    // Validate time formats (simple validation)
    const timeFields = [
      'check_in_start_time',
      'late_threshold_time',
      'half_day_missed_start_time',
      'break_time',
      'check_out_start_time',
      'early_leave_threshold_time',
      'half_day_missed_end_time',
      'flex_late_threshold_time',
      'flex_half_day_missed_time',
    ];

    for (const field of timeFields) {
      const timeValue = shiftDetailData[
        field as keyof typeof shiftDetailData
      ] as string;
      if (timeValue && !this.isValidTimeFormat(timeValue)) {
        throw new Error(`Invalid time format for ${field}`);
      }
    }

    // Create the new shift detail
    return this.create(shiftDetailData);
  }

  /**
   * Update a shift detail
   * @param id The shift detail ID
   * @param shiftDetailData The data to update
   * @returns True if the shift detail was updated, false otherwise
   */
  async updateShiftDetail(
    id: string,
    shiftDetailData: Partial<{
      shift_id: string;
      is_overnight: boolean;
      check_in_start_time: string;
      late_threshold_time: string;
      half_day_missed_start_time: string;
      break_time: string;
      check_out_start_time: string;
      early_leave_threshold_time: string;
      half_day_missed_end_time: string;
      total_working_hours: number;
      check_in_required: boolean;
      flex_late_threshold_time: string;
      flex_half_day_missed_time: string;
    }>,
  ): Promise<boolean> {
    // Check if the shift detail exists
    const shiftDetail = await this.findById(id);
    if (!shiftDetail) {
      throw new Error(`Shift detail with ID '${id}' not found`);
    }

    // Validate total working hours if provided
    if (
      shiftDetailData.total_working_hours !== undefined &&
      shiftDetailData.total_working_hours <= 0
    ) {
      throw new Error('Total working hours must be greater than 0');
    }

    // Validate time formats (simple validation)
    const timeFields = [
      'check_in_start_time',
      'late_threshold_time',
      'half_day_missed_start_time',
      'break_time',
      'check_out_start_time',
      'early_leave_threshold_time',
      'half_day_missed_end_time',
      'flex_late_threshold_time',
      'flex_half_day_missed_time',
    ];

    for (const field of timeFields) {
      const timeValue = shiftDetailData[
        field as keyof typeof shiftDetailData
      ] as string | undefined;
      if (timeValue && !this.isValidTimeFormat(timeValue)) {
        throw new Error(`Invalid time format for ${field}`);
      }
    }

    // Update the shift detail
    return this.update(id, shiftDetailData);
  }

  /**
   * Find shift details by shift ID
   * @param shiftId The shift ID
   * @returns An array of shift details
   */
  async findByShiftId(shiftId: string): Promise<ShiftDetailDocument[]> {
    return (this.repository as ShiftDetailRepository).findByShiftId(shiftId);
  }

  /**
   * Find overnight shift details
   * @returns An array of overnight shift details
   */
  async findOvernightShifts(): Promise<ShiftDetailDocument[]> {
    return (this.repository as ShiftDetailRepository).findOvernightShifts();
  }

  /**
   * Find shift details by total working hours
   * @param totalWorkingHours The total working hours
   * @returns An array of shift details
   */
  async findByTotalWorkingHours(
    totalWorkingHours: number,
  ): Promise<ShiftDetailDocument[]> {
    return (this.repository as ShiftDetailRepository).findByTotalWorkingHours(
      totalWorkingHours,
    );
  }

  /**
   * Find shift details by tenant ID
   * @param tenantId The tenant ID
   * @returns An array of shift details
   */
  async findByTenantId(tenantId: string): Promise<ShiftDetailDocument[]> {
    return (this.repository as ShiftDetailRepository).findByTenantId(tenantId);
  }

  /**
   * Get next code for tenant
   * @param tenantId The tenant ID
   * @returns The next code number
   */
  async getNextCode(tenantId: string): Promise<number> {
    return (this.repository as ShiftDetailRepository).getNextCode(tenantId);
  }

  /**
   * Create shift detail with auto-generated code
   * @param shiftDetailData The shift detail data
   * @returns The newly created shift detail
   */
  async createShiftDetailWithAutoCode(shiftDetailData: Partial<ShiftDetailDocument>): Promise<ShiftDetailDocument> {
    if (!shiftDetailData.tenant_id) {
      throw new Error('tenant_id is required');
    }

    const nextCode = await this.getNextCode(shiftDetailData.tenant_id);
    const createData = { ...shiftDetailData, code: nextCode };

    return this.create(createData);
  }

  /**
   * Delete shift details by shift ID
   * @param shiftId The shift ID
   * @returns True if any shift details were deleted, false otherwise
   */
  async deleteByShiftId(shiftId: string): Promise<boolean> {
    return (this.repository as ShiftDetailRepository).deleteByShiftId(shiftId);
  }

  /**
   * Validate time format (HH:MM)
   * @param time The time string to validate
   * @returns True if the time format is valid, false otherwise
   */
  private isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }
}

export default ShiftDetailService;
