import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { RecognitionDocument } from '@/database/entities/RecognitionModel';
import RecognitionRepository from '@/repositories/RecognitionRepository';

/**
 * Service for managing recognition logs
 * Extends the BaseModel with RecognitionDocument type
 */
@Injectable()
class RecognitionService extends BaseModel<RecognitionDocument> {
  /**
   * Create a new RecognitionService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(RecognitionRepository)
    private readonly recognitionRepository: RecognitionRepository,
    tenantId?: string,
  ) {
    super(recognitionRepository, tenantId);
  }

  /**
   * Find recognition logs by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<RecognitionDocument[]> {
    return this.recognitionRepository.findByTenantId(tenantId);
  }

  /**
   * Find recognition logs by recognize IDs
   */
  async findByRecognizeIds(recognizeIds: string[]): Promise<RecognitionDocument[]> {
    return this.recognitionRepository.findByRecognizeIds(recognizeIds);
  }

  /**
   * Find recognition logs by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<RecognitionDocument[]> {
    return this.recognitionRepository.findByDateRange(startDate, endDate);
  }

  /**
   * Find recognition logs by user mapping and date range
   * This method is used to get attendance data for a specific user
   */
  async findByUserMappingAndDateRange(
    recognizeIds: string[], 
    startDate: Date, 
    endDate: Date
  ): Promise<RecognitionDocument[]> {
    return this.recognitionRepository.findByUserMappingAndDateRange(recognizeIds, startDate, endDate);
  }
}

export default RecognitionService;
