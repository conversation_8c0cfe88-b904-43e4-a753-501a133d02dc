import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { ShiftAssignmentDetailDocument } from '@/database/entities/ShiftAssignmentDetailModel';
import ShiftAssignmentDetailRepository from '@/repositories/ShiftAssignmentDetailRepository';

/**
 * Service for managing shift assignment details
 * Extends the BaseModel with ShiftAssignmentDetailDocument type
 */
@Injectable()
class ShiftAssignmentDetailService extends BaseModel<ShiftAssignmentDetailDocument> {
  /**
   * Create a new ShiftAssignmentDetailService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(ShiftAssignmentDetailRepository)
    private readonly shiftAssignmentDetailRepository: ShiftAssignmentDetailRepository,
  ) {
    super(shiftAssignmentDetailRepository);
  }

  /**
   * Find shift assignment details by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<ShiftAssignmentDetailDocument[]> {
    return this.shiftAssignmentDetailRepository.findByTenantId(tenantId);
  }

  /**
   * Get next code for tenant
   */
  async getNextCode(tenantId: string): Promise<number> {
    return this.shiftAssignmentDetailRepository.getNextCode(tenantId);
  }

  /**
   * Create shift assignment detail with auto-generated code
   */
  async createWithAutoCode(data: Partial<ShiftAssignmentDetailDocument>): Promise<ShiftAssignmentDetailDocument> {
    if (!data.tenant_id) {
      throw new Error('tenant_id is required');
    }

    const nextCode = await this.getNextCode(data.tenant_id);
    const createData = { ...data, code: nextCode };

    return this.create(createData);
  }

  /**
   * Find all shift assignment details
   */
  async findAll(): Promise<ShiftAssignmentDetailDocument[]> {
    return this.find();
  }
}

export default ShiftAssignmentDetailService;
