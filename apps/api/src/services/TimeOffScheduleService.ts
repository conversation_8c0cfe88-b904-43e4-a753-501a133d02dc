import { BaseModel, Inject, Injectable } from '@c-cam/core';
import { TimeOffScheduleDocument } from '@/database/entities/TimeOffScheduleModel';
import TimeOffScheduleRepository from '@/repositories/TimeOffScheduleRepository';

/**
 * Service for managing time off schedules
 * Extends the BaseModel with TimeOffScheduleDocument type
 */
@Injectable()
class TimeOffScheduleService extends BaseModel<TimeOffScheduleDocument> {
  /**
   * Create a new TimeOffScheduleService
   * @param tenantId Optional tenant ID for multi-tenant operations
   */
  constructor(
    @Inject(TimeOffScheduleRepository)
    private readonly timeOffScheduleRepository: TimeOffScheduleRepository,
    tenantId?: string,
  ) {
    super(timeOffScheduleRepository, tenantId);
  }

  /**
   * Find time off schedules by tenant ID
   */
  async findByTenantId(tenantId: string): Promise<TimeOffScheduleDocument[]> {
    return this.timeOffScheduleRepository.findByTenantId(tenantId);
  }

  /**
   * Find time off schedules by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<TimeOffScheduleDocument[]> {
    return this.timeOffScheduleRepository.findByDateRange(startDate, endDate);
  }

  /**
   * Find active time off schedules
   */
  async findActive(): Promise<TimeOffScheduleDocument[]> {
    return this.timeOffScheduleRepository.findActive();
  }
}

export default TimeOffScheduleService;
