{"name": "@c-cam/web", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write 'src/**/*.{ts,tsx,js,jsx,css}'", "check-types": "tsc --noEmit"}, "dependencies": {"@c-cam/tracing-browser": "workspace:*", "@c-cam/types": "workspace:*", "@faker-js/faker": "^9.8.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.79.0", "@tanstack/react-router": "^1.120.13", "@tanstack/react-router-devtools": "^1.114.3", "@tanstack/react-table": "^8.21.3", "@tanstack/router-plugin": "^1.114.3", "@types/react-window": "^1.8.8", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-image-crop": "^11.0.10", "react-resizable-panels": "^3.0.2", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "vaul": "^1.1.2", "zod": "^3.25.46", "lodash": "^4.17.21"}, "devDependencies": {"@c-cam/eslint": "workspace:*", "@c-cam/tsconfig": "workspace:*", "@tanstack/eslint-config": "^0.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^22.15.21", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-window-infinite-loader": "^1.0.9", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.28.0", "jsdom": "^26.0.0", "prettier": "^3.5.3", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^5.0.2"}}