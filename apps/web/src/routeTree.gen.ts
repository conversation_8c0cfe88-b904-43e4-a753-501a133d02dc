/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as IndexImport } from './routes/index'
import { Route as UnitsIndexImport } from './routes/units/index'
import { Route as RolesIndexImport } from './routes/roles/index'
import { Route as MembersIndexImport } from './routes/members/index'
import { Route as EdgeDevicesIndexImport } from './routes/edge-devices/index'
import { Route as DashboardIndexImport } from './routes/dashboard/index'
import { Route as CamerasIndexImport } from './routes/cameras/index'
import { Route as SettingsAccountImport } from './routes/settings/account'
import { Route as RolesPermissionImport } from './routes/roles/permission'
import { Route as EdgeDevicesDetailImport } from './routes/edge-devices/detail'
import { Route as EdgeDevicesAddDeviceImport } from './routes/edge-devices/add-device'
import { Route as DevMockDataDemoImport } from './routes/dev/mock-data-demo'
import { Route as AuthLoginImport } from './routes/auth/login'
import { Route as AuthForgotPasswordImport } from './routes/auth/forgot-password'
import { Route as SystemOrganizationsIndexImport } from './routes/system/organizations/index'
import { Route as ComponentsDropdownIndexImport } from './routes/components/dropdown/index'
import { Route as ComponentsDatePickerIndexImport } from './routes/components/date-picker/index'
import { Route as AttendanceShiftsIndexImport } from './routes/attendance/shifts/index'
import { Route as AttendanceShiftAssignmentIndexImport } from './routes/attendance/shift-assignment/index'
import { Route as AttendanceLeaveScheduleIndexImport } from './routes/attendance/leave-schedule/index'
import { Route as AttendanceAttendanceCalendarIndexImport } from './routes/attendance/attendance-calendar/index'
import { Route as SystemOrganizationsDetailImport } from './routes/system/organizations/detail'
import { Route as AttendanceShiftAssignmentCreateIndexImport } from './routes/attendance/shift-assignment/create/index'

// Create/Update Routes

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const UnitsIndexRoute = UnitsIndexImport.update({
  id: '/units/',
  path: '/units/',
  getParentRoute: () => rootRoute,
} as any)

const RolesIndexRoute = RolesIndexImport.update({
  id: '/roles/',
  path: '/roles/',
  getParentRoute: () => rootRoute,
} as any)

const MembersIndexRoute = MembersIndexImport.update({
  id: '/members/',
  path: '/members/',
  getParentRoute: () => rootRoute,
} as any)

const EdgeDevicesIndexRoute = EdgeDevicesIndexImport.update({
  id: '/edge-devices/',
  path: '/edge-devices/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardIndexRoute = DashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => rootRoute,
} as any)

const CamerasIndexRoute = CamerasIndexImport.update({
  id: '/cameras/',
  path: '/cameras/',
  getParentRoute: () => rootRoute,
} as any)

const SettingsAccountRoute = SettingsAccountImport.update({
  id: '/settings/account',
  path: '/settings/account',
  getParentRoute: () => rootRoute,
} as any)

const RolesPermissionRoute = RolesPermissionImport.update({
  id: '/roles/permission',
  path: '/roles/permission',
  getParentRoute: () => rootRoute,
} as any)

const EdgeDevicesDetailRoute = EdgeDevicesDetailImport.update({
  id: '/edge-devices/detail',
  path: '/edge-devices/detail',
  getParentRoute: () => rootRoute,
} as any)

const EdgeDevicesAddDeviceRoute = EdgeDevicesAddDeviceImport.update({
  id: '/edge-devices/add-device',
  path: '/edge-devices/add-device',
  getParentRoute: () => rootRoute,
} as any)

const DevMockDataDemoRoute = DevMockDataDemoImport.update({
  id: '/dev/mock-data-demo',
  path: '/dev/mock-data-demo',
  getParentRoute: () => rootRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthForgotPasswordRoute = AuthForgotPasswordImport.update({
  id: '/auth/forgot-password',
  path: '/auth/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const SystemOrganizationsIndexRoute = SystemOrganizationsIndexImport.update({
  id: '/system/organizations/',
  path: '/system/organizations/',
  getParentRoute: () => rootRoute,
} as any)

const ComponentsDropdownIndexRoute = ComponentsDropdownIndexImport.update({
  id: '/components/dropdown/',
  path: '/components/dropdown/',
  getParentRoute: () => rootRoute,
} as any)

const ComponentsDatePickerIndexRoute = ComponentsDatePickerIndexImport.update({
  id: '/components/date-picker/',
  path: '/components/date-picker/',
  getParentRoute: () => rootRoute,
} as any)

const AttendanceShiftsIndexRoute = AttendanceShiftsIndexImport.update({
  id: '/attendance/shifts/',
  path: '/attendance/shifts/',
  getParentRoute: () => rootRoute,
} as any)

const AttendanceShiftAssignmentIndexRoute =
  AttendanceShiftAssignmentIndexImport.update({
    id: '/attendance/shift-assignment/',
    path: '/attendance/shift-assignment/',
    getParentRoute: () => rootRoute,
  } as any)

const AttendanceLeaveScheduleIndexRoute =
  AttendanceLeaveScheduleIndexImport.update({
    id: '/attendance/leave-schedule/',
    path: '/attendance/leave-schedule/',
    getParentRoute: () => rootRoute,
  } as any)

const AttendanceAttendanceCalendarIndexRoute =
  AttendanceAttendanceCalendarIndexImport.update({
    id: '/attendance/attendance-calendar/',
    path: '/attendance/attendance-calendar/',
    getParentRoute: () => rootRoute,
  } as any)

const SystemOrganizationsDetailRoute = SystemOrganizationsDetailImport.update({
  id: '/system/organizations/detail',
  path: '/system/organizations/detail',
  getParentRoute: () => rootRoute,
} as any)

const AttendanceShiftAssignmentCreateIndexRoute =
  AttendanceShiftAssignmentCreateIndexImport.update({
    id: '/attendance/shift-assignment/create/',
    path: '/attendance/shift-assignment/create/',
    getParentRoute: () => rootRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/auth/forgot-password': {
      id: '/auth/forgot-password'
      path: '/auth/forgot-password'
      fullPath: '/auth/forgot-password'
      preLoaderRoute: typeof AuthForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof rootRoute
    }
    '/dev/mock-data-demo': {
      id: '/dev/mock-data-demo'
      path: '/dev/mock-data-demo'
      fullPath: '/dev/mock-data-demo'
      preLoaderRoute: typeof DevMockDataDemoImport
      parentRoute: typeof rootRoute
    }
    '/edge-devices/add-device': {
      id: '/edge-devices/add-device'
      path: '/edge-devices/add-device'
      fullPath: '/edge-devices/add-device'
      preLoaderRoute: typeof EdgeDevicesAddDeviceImport
      parentRoute: typeof rootRoute
    }
    '/edge-devices/detail': {
      id: '/edge-devices/detail'
      path: '/edge-devices/detail'
      fullPath: '/edge-devices/detail'
      preLoaderRoute: typeof EdgeDevicesDetailImport
      parentRoute: typeof rootRoute
    }
    '/roles/permission': {
      id: '/roles/permission'
      path: '/roles/permission'
      fullPath: '/roles/permission'
      preLoaderRoute: typeof RolesPermissionImport
      parentRoute: typeof rootRoute
    }
    '/settings/account': {
      id: '/settings/account'
      path: '/settings/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof SettingsAccountImport
      parentRoute: typeof rootRoute
    }
    '/cameras/': {
      id: '/cameras/'
      path: '/cameras'
      fullPath: '/cameras'
      preLoaderRoute: typeof CamerasIndexImport
      parentRoute: typeof rootRoute
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardIndexImport
      parentRoute: typeof rootRoute
    }
    '/edge-devices/': {
      id: '/edge-devices/'
      path: '/edge-devices'
      fullPath: '/edge-devices'
      preLoaderRoute: typeof EdgeDevicesIndexImport
      parentRoute: typeof rootRoute
    }
    '/members/': {
      id: '/members/'
      path: '/members'
      fullPath: '/members'
      preLoaderRoute: typeof MembersIndexImport
      parentRoute: typeof rootRoute
    }
    '/roles/': {
      id: '/roles/'
      path: '/roles'
      fullPath: '/roles'
      preLoaderRoute: typeof RolesIndexImport
      parentRoute: typeof rootRoute
    }
    '/units/': {
      id: '/units/'
      path: '/units'
      fullPath: '/units'
      preLoaderRoute: typeof UnitsIndexImport
      parentRoute: typeof rootRoute
    }
    '/system/organizations/detail': {
      id: '/system/organizations/detail'
      path: '/system/organizations/detail'
      fullPath: '/system/organizations/detail'
      preLoaderRoute: typeof SystemOrganizationsDetailImport
      parentRoute: typeof rootRoute
    }
    '/attendance/attendance-calendar/': {
      id: '/attendance/attendance-calendar/'
      path: '/attendance/attendance-calendar'
      fullPath: '/attendance/attendance-calendar'
      preLoaderRoute: typeof AttendanceAttendanceCalendarIndexImport
      parentRoute: typeof rootRoute
    }
    '/attendance/leave-schedule/': {
      id: '/attendance/leave-schedule/'
      path: '/attendance/leave-schedule'
      fullPath: '/attendance/leave-schedule'
      preLoaderRoute: typeof AttendanceLeaveScheduleIndexImport
      parentRoute: typeof rootRoute
    }
    '/attendance/shift-assignment/': {
      id: '/attendance/shift-assignment/'
      path: '/attendance/shift-assignment'
      fullPath: '/attendance/shift-assignment'
      preLoaderRoute: typeof AttendanceShiftAssignmentIndexImport
      parentRoute: typeof rootRoute
    }
    '/attendance/shifts/': {
      id: '/attendance/shifts/'
      path: '/attendance/shifts'
      fullPath: '/attendance/shifts'
      preLoaderRoute: typeof AttendanceShiftsIndexImport
      parentRoute: typeof rootRoute
    }
    '/components/date-picker/': {
      id: '/components/date-picker/'
      path: '/components/date-picker'
      fullPath: '/components/date-picker'
      preLoaderRoute: typeof ComponentsDatePickerIndexImport
      parentRoute: typeof rootRoute
    }
    '/components/dropdown/': {
      id: '/components/dropdown/'
      path: '/components/dropdown'
      fullPath: '/components/dropdown'
      preLoaderRoute: typeof ComponentsDropdownIndexImport
      parentRoute: typeof rootRoute
    }
    '/system/organizations/': {
      id: '/system/organizations/'
      path: '/system/organizations'
      fullPath: '/system/organizations'
      preLoaderRoute: typeof SystemOrganizationsIndexImport
      parentRoute: typeof rootRoute
    }
    '/attendance/shift-assignment/create/': {
      id: '/attendance/shift-assignment/create/'
      path: '/attendance/shift-assignment/create'
      fullPath: '/attendance/shift-assignment/create'
      preLoaderRoute: typeof AttendanceShiftAssignmentCreateIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/dev/mock-data-demo': typeof DevMockDataDemoRoute
  '/edge-devices/add-device': typeof EdgeDevicesAddDeviceRoute
  '/edge-devices/detail': typeof EdgeDevicesDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/settings/account': typeof SettingsAccountRoute
  '/cameras': typeof CamerasIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/edge-devices': typeof EdgeDevicesIndexRoute
  '/members': typeof MembersIndexRoute
  '/roles': typeof RolesIndexRoute
  '/units': typeof UnitsIndexRoute
  '/system/organizations/detail': typeof SystemOrganizationsDetailRoute
  '/attendance/attendance-calendar': typeof AttendanceAttendanceCalendarIndexRoute
  '/attendance/leave-schedule': typeof AttendanceLeaveScheduleIndexRoute
  '/attendance/shift-assignment': typeof AttendanceShiftAssignmentIndexRoute
  '/attendance/shifts': typeof AttendanceShiftsIndexRoute
  '/components/date-picker': typeof ComponentsDatePickerIndexRoute
  '/components/dropdown': typeof ComponentsDropdownIndexRoute
  '/system/organizations': typeof SystemOrganizationsIndexRoute
  '/attendance/shift-assignment/create': typeof AttendanceShiftAssignmentCreateIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/dev/mock-data-demo': typeof DevMockDataDemoRoute
  '/edge-devices/add-device': typeof EdgeDevicesAddDeviceRoute
  '/edge-devices/detail': typeof EdgeDevicesDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/settings/account': typeof SettingsAccountRoute
  '/cameras': typeof CamerasIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/edge-devices': typeof EdgeDevicesIndexRoute
  '/members': typeof MembersIndexRoute
  '/roles': typeof RolesIndexRoute
  '/units': typeof UnitsIndexRoute
  '/system/organizations/detail': typeof SystemOrganizationsDetailRoute
  '/attendance/attendance-calendar': typeof AttendanceAttendanceCalendarIndexRoute
  '/attendance/leave-schedule': typeof AttendanceLeaveScheduleIndexRoute
  '/attendance/shift-assignment': typeof AttendanceShiftAssignmentIndexRoute
  '/attendance/shifts': typeof AttendanceShiftsIndexRoute
  '/components/date-picker': typeof ComponentsDatePickerIndexRoute
  '/components/dropdown': typeof ComponentsDropdownIndexRoute
  '/system/organizations': typeof SystemOrganizationsIndexRoute
  '/attendance/shift-assignment/create': typeof AttendanceShiftAssignmentCreateIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/auth/forgot-password': typeof AuthForgotPasswordRoute
  '/auth/login': typeof AuthLoginRoute
  '/dev/mock-data-demo': typeof DevMockDataDemoRoute
  '/edge-devices/add-device': typeof EdgeDevicesAddDeviceRoute
  '/edge-devices/detail': typeof EdgeDevicesDetailRoute
  '/roles/permission': typeof RolesPermissionRoute
  '/settings/account': typeof SettingsAccountRoute
  '/cameras/': typeof CamerasIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/edge-devices/': typeof EdgeDevicesIndexRoute
  '/members/': typeof MembersIndexRoute
  '/roles/': typeof RolesIndexRoute
  '/units/': typeof UnitsIndexRoute
  '/system/organizations/detail': typeof SystemOrganizationsDetailRoute
  '/attendance/attendance-calendar/': typeof AttendanceAttendanceCalendarIndexRoute
  '/attendance/leave-schedule/': typeof AttendanceLeaveScheduleIndexRoute
  '/attendance/shift-assignment/': typeof AttendanceShiftAssignmentIndexRoute
  '/attendance/shifts/': typeof AttendanceShiftsIndexRoute
  '/components/date-picker/': typeof ComponentsDatePickerIndexRoute
  '/components/dropdown/': typeof ComponentsDropdownIndexRoute
  '/system/organizations/': typeof SystemOrganizationsIndexRoute
  '/attendance/shift-assignment/create/': typeof AttendanceShiftAssignmentCreateIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/dev/mock-data-demo'
    | '/edge-devices/add-device'
    | '/edge-devices/detail'
    | '/roles/permission'
    | '/settings/account'
    | '/cameras'
    | '/dashboard'
    | '/edge-devices'
    | '/members'
    | '/roles'
    | '/units'
    | '/system/organizations/detail'
    | '/attendance/attendance-calendar'
    | '/attendance/leave-schedule'
    | '/attendance/shift-assignment'
    | '/attendance/shifts'
    | '/components/date-picker'
    | '/components/dropdown'
    | '/system/organizations'
    | '/attendance/shift-assignment/create'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/dev/mock-data-demo'
    | '/edge-devices/add-device'
    | '/edge-devices/detail'
    | '/roles/permission'
    | '/settings/account'
    | '/cameras'
    | '/dashboard'
    | '/edge-devices'
    | '/members'
    | '/roles'
    | '/units'
    | '/system/organizations/detail'
    | '/attendance/attendance-calendar'
    | '/attendance/leave-schedule'
    | '/attendance/shift-assignment'
    | '/attendance/shifts'
    | '/components/date-picker'
    | '/components/dropdown'
    | '/system/organizations'
    | '/attendance/shift-assignment/create'
  id:
    | '__root__'
    | '/'
    | '/auth/forgot-password'
    | '/auth/login'
    | '/dev/mock-data-demo'
    | '/edge-devices/add-device'
    | '/edge-devices/detail'
    | '/roles/permission'
    | '/settings/account'
    | '/cameras/'
    | '/dashboard/'
    | '/edge-devices/'
    | '/members/'
    | '/roles/'
    | '/units/'
    | '/system/organizations/detail'
    | '/attendance/attendance-calendar/'
    | '/attendance/leave-schedule/'
    | '/attendance/shift-assignment/'
    | '/attendance/shifts/'
    | '/components/date-picker/'
    | '/components/dropdown/'
    | '/system/organizations/'
    | '/attendance/shift-assignment/create/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthForgotPasswordRoute: typeof AuthForgotPasswordRoute
  AuthLoginRoute: typeof AuthLoginRoute
  DevMockDataDemoRoute: typeof DevMockDataDemoRoute
  EdgeDevicesAddDeviceRoute: typeof EdgeDevicesAddDeviceRoute
  EdgeDevicesDetailRoute: typeof EdgeDevicesDetailRoute
  RolesPermissionRoute: typeof RolesPermissionRoute
  SettingsAccountRoute: typeof SettingsAccountRoute
  CamerasIndexRoute: typeof CamerasIndexRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  EdgeDevicesIndexRoute: typeof EdgeDevicesIndexRoute
  MembersIndexRoute: typeof MembersIndexRoute
  RolesIndexRoute: typeof RolesIndexRoute
  UnitsIndexRoute: typeof UnitsIndexRoute
  SystemOrganizationsDetailRoute: typeof SystemOrganizationsDetailRoute
  AttendanceAttendanceCalendarIndexRoute: typeof AttendanceAttendanceCalendarIndexRoute
  AttendanceLeaveScheduleIndexRoute: typeof AttendanceLeaveScheduleIndexRoute
  AttendanceShiftAssignmentIndexRoute: typeof AttendanceShiftAssignmentIndexRoute
  AttendanceShiftsIndexRoute: typeof AttendanceShiftsIndexRoute
  ComponentsDatePickerIndexRoute: typeof ComponentsDatePickerIndexRoute
  ComponentsDropdownIndexRoute: typeof ComponentsDropdownIndexRoute
  SystemOrganizationsIndexRoute: typeof SystemOrganizationsIndexRoute
  AttendanceShiftAssignmentCreateIndexRoute: typeof AttendanceShiftAssignmentCreateIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthForgotPasswordRoute: AuthForgotPasswordRoute,
  AuthLoginRoute: AuthLoginRoute,
  DevMockDataDemoRoute: DevMockDataDemoRoute,
  EdgeDevicesAddDeviceRoute: EdgeDevicesAddDeviceRoute,
  EdgeDevicesDetailRoute: EdgeDevicesDetailRoute,
  RolesPermissionRoute: RolesPermissionRoute,
  SettingsAccountRoute: SettingsAccountRoute,
  CamerasIndexRoute: CamerasIndexRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  EdgeDevicesIndexRoute: EdgeDevicesIndexRoute,
  MembersIndexRoute: MembersIndexRoute,
  RolesIndexRoute: RolesIndexRoute,
  UnitsIndexRoute: UnitsIndexRoute,
  SystemOrganizationsDetailRoute: SystemOrganizationsDetailRoute,
  AttendanceAttendanceCalendarIndexRoute:
    AttendanceAttendanceCalendarIndexRoute,
  AttendanceLeaveScheduleIndexRoute: AttendanceLeaveScheduleIndexRoute,
  AttendanceShiftAssignmentIndexRoute: AttendanceShiftAssignmentIndexRoute,
  AttendanceShiftsIndexRoute: AttendanceShiftsIndexRoute,
  ComponentsDatePickerIndexRoute: ComponentsDatePickerIndexRoute,
  ComponentsDropdownIndexRoute: ComponentsDropdownIndexRoute,
  SystemOrganizationsIndexRoute: SystemOrganizationsIndexRoute,
  AttendanceShiftAssignmentCreateIndexRoute:
    AttendanceShiftAssignmentCreateIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/auth/forgot-password",
        "/auth/login",
        "/dev/mock-data-demo",
        "/edge-devices/add-device",
        "/edge-devices/detail",
        "/roles/permission",
        "/settings/account",
        "/cameras/",
        "/dashboard/",
        "/edge-devices/",
        "/members/",
        "/roles/",
        "/units/",
        "/system/organizations/detail",
        "/attendance/attendance-calendar/",
        "/attendance/leave-schedule/",
        "/attendance/shift-assignment/",
        "/attendance/shifts/",
        "/components/date-picker/",
        "/components/dropdown/",
        "/system/organizations/",
        "/attendance/shift-assignment/create/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/auth/forgot-password": {
      "filePath": "auth/forgot-password.tsx"
    },
    "/auth/login": {
      "filePath": "auth/login.tsx"
    },
    "/dev/mock-data-demo": {
      "filePath": "dev/mock-data-demo.tsx"
    },
    "/edge-devices/add-device": {
      "filePath": "edge-devices/add-device.tsx"
    },
    "/edge-devices/detail": {
      "filePath": "edge-devices/detail.tsx"
    },
    "/roles/permission": {
      "filePath": "roles/permission.tsx"
    },
    "/settings/account": {
      "filePath": "settings/account.tsx"
    },
    "/cameras/": {
      "filePath": "cameras/index.tsx"
    },
    "/dashboard/": {
      "filePath": "dashboard/index.tsx"
    },
    "/edge-devices/": {
      "filePath": "edge-devices/index.tsx"
    },
    "/members/": {
      "filePath": "members/index.tsx"
    },
    "/roles/": {
      "filePath": "roles/index.tsx"
    },
    "/units/": {
      "filePath": "units/index.tsx"
    },
    "/system/organizations/detail": {
      "filePath": "system/organizations/detail.tsx"
    },
    "/attendance/attendance-calendar/": {
      "filePath": "attendance/attendance-calendar/index.tsx"
    },
    "/attendance/leave-schedule/": {
      "filePath": "attendance/leave-schedule/index.tsx"
    },
    "/attendance/shift-assignment/": {
      "filePath": "attendance/shift-assignment/index.tsx"
    },
    "/attendance/shifts/": {
      "filePath": "attendance/shifts/index.tsx"
    },
    "/components/date-picker/": {
      "filePath": "components/date-picker/index.tsx"
    },
    "/components/dropdown/": {
      "filePath": "components/dropdown/index.tsx"
    },
    "/system/organizations/": {
      "filePath": "system/organizations/index.tsx"
    },
    "/attendance/shift-assignment/create/": {
      "filePath": "attendance/shift-assignment/create/index.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
