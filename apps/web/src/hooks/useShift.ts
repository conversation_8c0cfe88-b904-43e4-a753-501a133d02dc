import { useState } from 'react';
import { toast } from 'sonner';

export interface ShiftFormData {
  name: string;
  type: 'Fixed' | 'Flexible';
  work_coefficient: number;
  tenant_id: string;
}

export interface ShiftDetailFormData {
  name: string;
  work_shift_type: 'Administrative' | 'Shift';
  entity_type: 'All' | 'Custom';
  assignment_type: 'Weekly' | 'FixedDay';
  dateRange_type: 'AboutDays' | 'InfinityDay';
  start_date: Date;
  end_date: Date;
  status: 'active' | 'inactive';
  targets: string[];
  excluded_targets: string[];
}

export interface CreateShiftRequest {
  shiftData: ShiftFormData;
  shiftDetailData: ShiftDetailFormData;
}

export const useShift = () => {
  const [loading, setLoading] = useState(false);

  const createShift = async (data: CreateShiftRequest) => {
    setLoading(true);
    try {
      const response = await fetch('/api/shifts/with-detail', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create shift');
      }

      const result = await response.json();
      toast.success('Tạo ca làm việc thành công');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getShifts = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/shifts');
      
      if (!response.ok) {
        throw new Error('Failed to fetch shifts');
      }

      const result = await response.json();
      return result.shifts || [];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    createShift,
    getShifts,
  };
};
