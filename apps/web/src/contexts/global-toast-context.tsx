import React, { createContext, useContext, useCallback, useState, useEffect } from 'react';
import { toast as sonnerToast } from 'sonner';
import { getToastIcon } from '@/components/shared/icons/components/ToastIcons'

interface ToastMessage {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  delay?: number; // in seconds, default 2s
  id?: string;
}

interface GlobalToastContextType {
  showToast: (toast: ToastMessage) => void;
}

interface GlobalToastProviderProps {
  children: React.ReactNode;
}

// Global state for toast notifications
let globalToastState: ToastMessage | null = null;
let globalToastListeners: ((toast: ToastMessage | null) => void)[] = [];

// Global functions to manage toast state - can be used anywhere
export const setGlobalToast = (toast: ToastMessage | null) => {
  globalToastState = toast;
  globalToastListeners.forEach(listener => listener(toast));
};

export const getGlobalToast = () => globalToastState;

export const subscribeToGlobalToast = (listener: (toast: ToastMessage | null) => void) => {
  globalToastListeners.push(listener);
  return () => {
    globalToastListeners = globalToastListeners.filter(l => l !== listener);
  };
};

// Utility functions for easy usage in any component
export const showGlobalToast = {
  success: (message: string, delay?: number, id?: string) => {
    setGlobalToast({ type: 'success', message, delay, id });
  },
  error: (message: string, delay?: number, id?: string) => {
    setGlobalToast({ type: 'error', message, delay, id });
  },
  warning: (message: string, delay?: number, id?: string) => {
    setGlobalToast({ type: 'warning', message, delay, id });
  },
  info: (message: string, delay?: number, id?: string) => {
    setGlobalToast({ type: 'info', message, delay, id });
  },
};

const GlobalToastContext = createContext<GlobalToastContextType | undefined>(undefined);

export function GlobalToastProvider({ children }: GlobalToastProviderProps) {
  const [currentToast, setCurrentToast] = useState<ToastMessage | null>(null);

  // Subscribe to global toast state changes
  useEffect(() => {
    const unsubscribe = subscribeToGlobalToast((toast) => {
      setCurrentToast(toast);
    });
    return unsubscribe;
  }, []);

  // Render toast when currentToast changes
  useEffect(() => {
    if (currentToast) {
      const duration = (currentToast.delay || 2) * 1000; // Convert seconds to milliseconds

      // Use custom toast with custom icons
      sonnerToast.custom(
        (t) => (
          <div className="flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg shadow-lg min-w-[300px]">
            {getToastIcon(currentToast.type, 20)}
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                {currentToast.message}
              </p>
            </div>
            <button
              onClick={() => sonnerToast.dismiss(t)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 6L6 18M6 6l12 12"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        ),
        {
          id: currentToast.id,
          duration,
        }
      );

      // Clear the toast state after showing
      setTimeout(() => {
        setGlobalToast(null);
      }, 100);
    }
  }, [currentToast]);

  const showToast = useCallback((toast: ToastMessage) => {
    setGlobalToast(toast);
  }, []);

  const value = {
    showToast,
  };

  return (
    <GlobalToastContext.Provider value={value}>
      {children}
    </GlobalToastContext.Provider>
  );
}

export function useGlobalToast() {
  const context = useContext(GlobalToastContext);
  if (context === undefined) {
    throw new Error('useGlobalToast must be used within a GlobalToastProvider');
  }
  return context;
}

// Export types for external usage
export type { ToastMessage };
