// Utility file for easy global toast usage
import { showGlobalToast, setGlobalToast, type ToastMessage } from '@/contexts/global-toast-context';

/**
 * Global Toast Utilities
 *
 * Usage examples:
 *
 * // Method 1: Using utility functions (recommended)
 * import { globalToast } from '@/utils/global-toast';
 *
 * globalToast.success('Thêm mới khách hàng thành công');
 * globalToast.error('Có lỗi xảy ra', 3); // 3 seconds
 * globalToast.warning('Cảnh báo', 5, 'warning-id');
 * globalToast.info('Thông tin');
 *
 * // Method 2: Using direct functions
 * import { showGlobalToast } from '@/utils/global-toast';
 *
 * showGlobalToast.success('Success message');
 * showGlobalToast.error('Error message', 4);
 *
 * // Method 3: Using setGlobalToast directly
 * import { setGlobalToast } from '@/utils/global-toast';
 *
 * setGlobalToast({
 *   type: 'success',
 *   message: 'Custom message',
 *   delay: 3,
 *   id: 'custom-id'
 * });
 */

// Re-export for convenience
export { showGlobalToast, setGlobalToast, type ToastMessage };

// Enhanced utility object with better API
export const globalToast = {
  /**
   * Show success toast
   * @param message - Toast message
   * @param delay - Duration in seconds (default: 2)
   * @param id - Optional unique ID
   */
  success: (message: string, id?: string, delay?: number) => {
    if (delay === undefined) {
      delay = 2;
    }
    showGlobalToast.success(message, delay, id);
  },

  /**
   * Show error toast
   * @param message - Toast message
   * @param delay - Duration in seconds (default: 2)
   * @param id - Optional unique ID
   */
  error: (message: string, id?: string, delay?: number) => {
    if (delay === undefined) {
      delay = 2;
    }
    showGlobalToast.error(message, delay, id);
  },

  /**
   * Show warning toast
   * @param message - Toast message
   * @param delay - Duration in seconds (default: 2)
   * @param id - Optional unique ID
   */
  warning: (message: string,  id?: string, delay?: number) => {
    if (delay === undefined) {
      delay = 2;
    }
    showGlobalToast.warning(message, delay, id);
  },

  /**
   * Show info toast
   * @param message - Toast message
   * @param delay - Duration in seconds (default: 2)
   * @param id - Optional unique ID
   */
  info: (message: string,  id?: string, delay?: number) => {
    if (delay === undefined) {
      delay = 2;
    }
    showGlobalToast.info(message, delay, id);
  },

  /**
   * Show custom toast
   * @param toast - Toast configuration object
   */
  custom: (toast: ToastMessage) => {
    setGlobalToast(toast);
  },
};

// Default export for convenience
export default globalToast;
