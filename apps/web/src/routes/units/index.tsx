import { createFileRoute } from '@tanstack/react-router'
import ProtectedLayout from '@/components/layout/protected.layout'

export const Route = createFileRoute('/units/')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <ProtectedLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Quản lý đơn vị tổ chức</h1>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Unit Selector Component</h2>
            <p className="text-gray-500">Component sẽ được thêm sau</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Thông tin</h2>
            <div className="space-y-3 text-sm">
              <div>
                <strong>T<PERSON>h năng:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Hiển thị cây đơn vị tổ chức</li>
                  <li>Tìm kiếm theo tên đơn vị</li>
                  <li>Expand/collapse các node có children</li>
                  <li>Chọn đơn vị hoặc root organization</li>
                  <li>Responsive design</li>
                </ul>
              </div>
              <div>
                <strong>Cách sử dụng:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Click vào tên đơn vị để chọn</li>
                  <li>Click vào mũi tên để expand/collapse</li>
                  <li>Sử dụng ô tìm kiếm để lọc</li>
                  <li>Click vào tên tổ chức để chọn root</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
