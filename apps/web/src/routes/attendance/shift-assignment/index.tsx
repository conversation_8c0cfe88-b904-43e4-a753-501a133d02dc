import ProtectedLayout from '@/components/layout/protected.layout'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createFileRoute, Link } from '@tanstack/react-router'
import { Users, Plus, Search, Calendar, Clock } from 'lucide-react'

export const Route = createFileRoute('/attendance/shift-assignment/')({
  component: RouteComponent,
})

interface ShiftAssignment {
  id: string
  employeeName: string
  shiftName: string
  date: string
  startTime: string
  endTime: string
  status: 'assigned' | 'completed' | 'absent'
  unit: string
}

const mockAssignmentData: ShiftAssignment[] = [
  {
    id: '1',
    employeeName: 'Nguyễn Văn A',
    shiftName: 'Ca sáng',
    date: '2024-01-20',
    startTime: '08:00',
    endTime: '16:00',
    status: 'assigned',
    unit: 'Phòng IT'
  },
  {
    id: '2',
    employeeName: 'Trần Thị B',
    shiftName: 'Ca chiều',
    date: '2024-01-20',
    startTime: '14:00',
    endTime: '22:00',
    status: 'completed',
    unit: 'Phòng HR'
  },
  {
    id: '3',
    employeeName: 'Lê Văn C',
    shiftName: 'Ca đêm',
    date: '2024-01-19',
    startTime: '22:00',
    endTime: '06:00',
    status: 'absent',
    unit: 'Bảo vệ'
  }
]

function RouteComponent() {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Badge className="bg-blue-100 text-blue-800">Đã phân ca</Badge>
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Hoàn thành</Badge>
      case 'absent':
        return <Badge className="bg-red-100 text-red-800">Vắng mặt</Badge>
      default:
        return <Badge>Không xác định</Badge>
    }
  }

  return (
    <ProtectedLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold flex items-center gap-2">
              <Users className="h-6 w-6" />
              Phân ca làm việc
            </h1>
            <p className="text-gray-600 mt-1">Quản lý phân ca và lịch làm việc của nhân viên</p>
          </div>
          <Link to="/attendance/shift-assignment/create">
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Tạo phân ca mới
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Hôm nay</p>
                  <p className="text-xl font-semibold">12</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">Hoàn thành</p>
                  <p className="text-xl font-semibold">8</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-yellow-500" />
                <div>
                  <p className="text-sm text-gray-600">Đang làm</p>
                  <p className="text-xl font-semibold">3</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-8 w-8 text-red-500" />
                <div>
                  <p className="text-sm text-gray-600">Vắng mặt</p>
                  <p className="text-xl font-semibold">1</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex gap-4">
          <div className="flex-1 max-w-sm">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input placeholder="Tìm kiếm nhân viên..." className="pl-10" />
            </div>
          </div>
        </div>

        {/* Assignments Table */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách phân ca</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr className="text-left">
                    <th className="pb-3 font-medium">Nhân viên</th>
                    <th className="pb-3 font-medium">Đơn vị</th>
                    <th className="pb-3 font-medium">Ca làm việc</th>
                    <th className="pb-3 font-medium">Ngày</th>
                    <th className="pb-3 font-medium">Giờ làm việc</th>
                    <th className="pb-3 font-medium">Trạng thái</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {mockAssignmentData.map((assignment) => (
                    <tr key={assignment.id} className="hover:bg-gray-50">
                      <td className="py-3 font-medium">{assignment.employeeName}</td>
                      <td className="py-3">{assignment.unit}</td>
                      <td className="py-3">{assignment.shiftName}</td>
                      <td className="py-3">{assignment.date}</td>
                      <td className="py-3">
                        {assignment.startTime} - {assignment.endTime}
                      </td>
                      <td className="py-3">{getStatusBadge(assignment.status)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
