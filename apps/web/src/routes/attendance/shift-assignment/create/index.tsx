import ProtectedLayout from '@/components/layout/protected.layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { createFileRoute, Link } from '@tanstack/react-router'
import { ArrowLeft, Save, Users, Calendar } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/attendance/shift-assignment/create/')({
  component: RouteComponent,
})

function RouteComponent() {
  const [formData, setFormData] = useState({
    employeeId: '',
    shiftId: '',
    date: '',
    startTime: '',
    endTime: '',
    notes: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Creating shift assignment:', formData)
    // Handle form submission here
  }

  return (
    <ProtectedLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link to="/attendance/shift-assignment">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold flex items-center gap-2">
              <Users className="h-6 w-6" />
              Tạo phân ca mới
            </h1>
            <p className="text-gray-600 mt-1">Phân ca làm việc cho nhân viên</p>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Thông tin phân ca
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="employee">Nhân viên *</Label>
                    <Select value={formData.employeeId} onValueChange={(value) =>
                      setFormData(prev => ({ ...prev, employeeId: value }))
                    }>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn nhân viên" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="emp1">Nguyễn Văn A</SelectItem>
                        <SelectItem value="emp2">Trần Thị B</SelectItem>
                        <SelectItem value="emp3">Lê Văn C</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="shift">Ca làm việc *</Label>
                    <Select value={formData.shiftId} onValueChange={(value) =>
                      setFormData(prev => ({ ...prev, shiftId: value }))
                    }>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn ca làm việc" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="morning">Ca sáng (08:00 - 16:00)</SelectItem>
                        <SelectItem value="afternoon">Ca chiều (14:00 - 22:00)</SelectItem>
                        <SelectItem value="night">Ca đêm (22:00 - 06:00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Ngày làm việc *</Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="startTime">Giờ bắt đầu *</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={formData.startTime}
                      onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="endTime">Giờ kết thúc *</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={formData.endTime}
                      onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Ghi chú</Label>
                  <Textarea
                    id="notes"
                    placeholder="Ghi chú thêm về ca làm việc..."
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <Button type="submit" className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Tạo phân ca
                  </Button>
                  <Link to="/attendance/shift-assignment">
                    <Button type="button" variant="outline">
                      Hủy bỏ
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  )
}
