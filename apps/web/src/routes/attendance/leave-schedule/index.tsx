import ProtectedLayout from '@/components/layout/protected.layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { createFileRoute } from '@tanstack/react-router'
import { Calendar, Plus, Search, Filter } from 'lucide-react'

export const Route = createFileRoute('/attendance/leave-schedule/')({
  component: RouteComponent,
})

interface LeaveRequest {
  id: string
  employeeName: string
  leaveType: string
  startDate: string
  endDate: string
  days: number
  status: 'pending' | 'approved' | 'rejected'
  reason: string
}

const mockLeaveData: LeaveRequest[] = [
  {
    id: '1',
    employeeName: 'Nguyễn Văn A',
    leaveType: 'Nghỉ phép',
    startDate: '2024-01-20',
    endDate: '2024-01-22',
    days: 3,
    status: 'approved',
    reason: 'Nghỉ phép cá nhân'
  },
  {
    id: '2',
    employeeName: 'Trần Thị B',
    leaveType: 'Nghỉ ốm',
    startDate: '2024-01-25',
    endDate: '2024-01-25',
    days: 1,
    status: 'pending',
    reason: 'Khám bệnh'
  }
]

function RouteComponent() {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Đã duyệt</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Chờ duyệt</Badge>
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Từ chối</Badge>
      default:
        return <Badge>Không xác định</Badge>
    }
  }

  return (
    <ProtectedLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold flex items-center gap-2">
              <Calendar className="h-6 w-6" />
              Lịch nghỉ phép
            </h1>
            <p className="text-gray-600 mt-1">Quản lý đơn xin nghỉ phép của nhân viên</p>
          </div>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Tạo đơn nghỉ phép
          </Button>
        </div>

        {/* Filters */}
        <div className="flex gap-4">
          <div className="flex-1 max-w-sm">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input placeholder="Tìm kiếm nhân viên..." className="pl-10" />
            </div>
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Bộ lọc
          </Button>
        </div>

        {/* Leave Requests Table */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách đơn nghỉ phép</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr className="text-left">
                    <th className="pb-3 font-medium">Nhân viên</th>
                    <th className="pb-3 font-medium">Loại nghỉ</th>
                    <th className="pb-3 font-medium">Thời gian</th>
                    <th className="pb-3 font-medium">Số ngày</th>
                    <th className="pb-3 font-medium">Trạng thái</th>
                    <th className="pb-3 font-medium">Lý do</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {mockLeaveData.map((leave) => (
                    <tr key={leave.id} className="hover:bg-gray-50">
                      <td className="py-3 font-medium">{leave.employeeName}</td>
                      <td className="py-3">{leave.leaveType}</td>
                      <td className="py-3">
                        <div className="text-sm">
                          <div>{leave.startDate}</div>
                          {leave.startDate !== leave.endDate && (
                            <div className="text-gray-500">đến {leave.endDate}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-3">{leave.days} ngày</td>
                      <td className="py-3">{getStatusBadge(leave.status)}</td>
                      <td className="py-3 text-sm text-gray-600">{leave.reason}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
