import { EntityType } from './shiftDetail.types';
import { Status } from './status.types';

/**
 * Day Off Type Enum
 */
export enum DayOffType {
  PublicHoliday = 'PublicHoliday', // Nghỉ lễ
  CompensatoryLeave = 'CompensatoryLeave', // Nghỉ bù
  UnplannedLeave = 'UnplannedLeave' // Nghỉ bất thường
}

/**
 * Display labels for DayOffType enum
 */
export const DayOffTypeDisplay = {
  [DayOffType.PublicHoliday]: 'Nghỉ lễ',
  [DayOffType.CompensatoryLeave]: 'Nghỉ bù',
  [DayOffType.UnplannedLeave]: 'Nghỉ bất thường'
};

/**
 * Time Off Schedule Attributes Interface
 */
export interface TimeOffScheduleAttributes {
  _id: string;
  tenant_id: string[];
  name: string;
  type: DayOffType;
  entity_type: EntityType;
  startDate: Date;
  endDate: Date;
  status: Status;
  targets: string[];
  excludedTargets: string[];
  createdBy: string;
  created_at?: Date;
  updated_at?: Date;
}
