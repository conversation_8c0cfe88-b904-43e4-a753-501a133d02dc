/**
 * Days of Week Enum
 */
export enum DaysOfWeek {
  Monday = 'Monday',
  Tuesday = 'Tuesday',
  Wednesday = 'Wednesday',
  Thursday = 'Thursday',
  Friday = 'Friday',
  Saturday = 'Saturday',
  Sunday = 'Sunday'
}

/**
 * Display labels for DaysOfWeek enum
 */
export const DaysOfWeekDisplay = {
  [DaysOfWeek.Monday]: 'Thứ 2',
  [DaysOfWeek.Tuesday]: 'Thứ 3',
  [DaysOfWeek.Wednesday]: 'Thứ 4',
  [DaysOfWeek.Thursday]: 'Thứ 5',
  [DaysOfWeek.Friday]: 'Thứ 6',
  [DaysOfWeek.Saturday]: 'Thứ 7',
  [DaysOfWeek.Sunday]: '<PERSON><PERSON> nhật'
};

/**
 * Shift Assignment Attributes Interface
 */
export interface ShiftAssignmentAttributes {
  _id: string;
  tenant_id: string;
  shift_id: string;
  days_of_week?: DaysOfWeek[];
  days?: Date[];
  created_at?: Date;
  updated_at?: Date;
}
