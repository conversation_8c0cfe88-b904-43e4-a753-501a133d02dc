import { Status } from './status.types';

/**
 * Work Shift Type Enum
 */
export enum WorkShiftType {
  Administrative = 'Administrative', // hành chính
  Shift = 'Shift' // ca kíp
}

/**
 * Display labels for WorkShiftType enum
 */
export const WorkShiftTypeDisplay = {
  [WorkShiftType.Administrative]: 'Hành chính',
  [WorkShiftType.Shift]: 'Ca kíp'
};

/**
 * Entity Type Enum
 */
export enum EntityType {
  All = 'All',
  Custom = 'Custom'
}

/**
 * Display labels for EntityType enum
 */
export const EntityTypeDisplay = {
  [EntityType.All]: 'Tất cả',
  [EntityType.Custom]: 'Tùy chỉnh'
};

/**
 * Shift Assignment Type Enum
 */
export enum ShiftAssignmentType {
  Weekly = 'Weekly',
  FixedDay = 'FixedDay'
}

/**
 * Display labels for ShiftAssignmentType enum
 */
export const ShiftAssignmentTypeDisplay = {
  [ShiftAssignmentType.Weekly]: '<PERSON> tuần',
  [ShiftAssignmentType.FixedDay]: 'Ngày cố định'
};

/**
 * Date Range Type Enum
 */
export enum DateRangeType {
  AboutDays = 'AboutDays',
  InfinityDay = 'InfinityDay'
}

/**
 * Display labels for DateRangeType enum
 */
export const DateRangeTypeDisplay = {
  [DateRangeType.AboutDays]: 'Khoảng ngày',
  [DateRangeType.InfinityDay]: 'Vô hạn'
};

/**
 * Shift Detail Attributes Interface
 * Defines the core data structure for shift details
 */
export interface ShiftDetailAttributes {
  _id: string;
  tenant_id: string;
  shift_assignment: string[];
  code: number;
  name: string;
  work_shift_type: WorkShiftType;
  entity_type: EntityType;
  assignment_type: ShiftAssignmentType;
  dateRange_type: DateRangeType;
  start_date: Date;
  end_date: Date;
  status: Status;
  created_by: string;
  targets: string[];
  excluded_targets: string[];
  created_at?: Date;
  updated_at?: Date;
}
