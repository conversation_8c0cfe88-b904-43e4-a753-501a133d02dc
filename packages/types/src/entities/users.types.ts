/**
 * User Status Enum
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

/**
 * User Gender Enum
 */
export enum UserGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

/**
 * Users Attributes Interface
 * Defines the core data structure for users
 */
export interface UsersAttributes {
  id: string;
  unit_id: string;
  face_id?: string;
  avatar_id?: string;
  member_role_id?: string;
  code: string;
  name: string;
  email?: string;
  phone?: string;
  dob?: Date;
  gender?: UserGender;
  username: string;
  password: string;
  status: UserStatus;
  created_by: string;
  mapping?: string[];
  created_at: Date;
  updated_at?: Date;
}

/**
 * User Response Interface (without sensitive data)
 */
export interface UserResponse extends Omit<UsersAttributes, 'password'> {
  // Additional computed fields can be added here
  full_name?: string;
  first_name?: string;
  last_name?: string;
  // Populated fields from backend
  tenant?: {
    id: string;
    name: string;
  };
  unit?: {
    id: string;
    name: string;
  };
}

/**
 * User Query Parameters Interface
 */
export interface UserQueryParams {
  limit?: number;
  skip?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  status?: UserStatus;
  unit_id?: string;
  search?: string;
}

/**
 * User List Response Interface
 */
export interface UserListResponse {
  users: UserResponse[];
  total?: number;
  page?: number;
  limit?: number;
}

/**
 * Single User Response Interface
 */
export interface SingleUserResponse {
  user: UserResponse;
}
