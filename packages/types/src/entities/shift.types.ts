/**
 * Shift Type Enum
 */
export enum ShiftType {
  Fixed = 'Fixed', // cố định
  Flexible = 'Flexible' // linh hoạt
}

/**
 * Display labels for ShiftType enum
 */
export const ShiftTypeDisplay = {
  [ShiftType.Fixed]: 'Cố định',
  [ShiftType.Flexible]: 'Linh hoạt'
};

/**
 * Shift Attributes Interface
 * Defines the core data structure for shifts
 */
export interface ShiftAttributes {
  _id: string;
  tenant_id: string;
  shift_detail_id: string;
  code: number;
  name: string;
  type: ShiftType;
  work_coefficient: number;
  created_by: string;
  created_at?: Date;
  updated_at?: Date;
}
