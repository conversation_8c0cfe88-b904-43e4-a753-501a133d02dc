import { WorkShiftType, EntityType, ShiftAssignmentType, DateRangeType } from './shiftDetail.types';
import { Status } from './status.types';

/**
 * Shift Assignment Detail Attributes Interface
 */
export interface ShiftAssignmentDetailAttributes {
  _id: string;
  tenant_id: string;
  shift_assignment_ids: string[];
  code: number;
  name: string;
  work_shift_type: WorkShiftType;
  entity_type: EntityType;
  assignment_type: ShiftAssignmentType;
  dateRange_type: DateRangeType;
  start_date: Date;
  end_date: Date;
  status: Status;
  createdBy: string;
  targets: string[];
  excludedTargets: string[];
  created_at?: Date;
  updated_at?: Date;
}
